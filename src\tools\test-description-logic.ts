/**
 * Test the improved mod description logic
 */

// Simulate the getModDescription function logic
function getModDescription(modData: any): string {
  // Use actual description from StringTable if available
  if (modData?.actualDescription) {
    return modData.actualDescription;
  }

  // NEW: Use extracted mod name and author to create a meaningful description
  if (modData?.actualModName || modData?.author) {
    const modName = modData.actualModName || 'Custom Content';
    const author = modData.author;
    
    if (author && modName) {
      return `${modName} by ${author} - Custom content for The Sims 4.`;
    } else if (modName) {
      return `${modName} - Custom content for The Sims 4.`;
    } else if (author) {
      return `Custom content by ${author} for The Sims 4.`;
    }
  }

  // Fallback
  return 'Custom content for The Sims 4.';
}

// Test cases
const testCases = [
  {
    name: 'Full metadata',
    data: {
      actualModName: 'SimDaDatingApp NoFWord',
      author: '!LittleMsSam',
      actualDescription: null
    }
  },
  {
    name: 'Only mod name',
    data: {
      actualModName: 'Beautiful Hair Pack',
      author: null,
      actualDescription: null
    }
  },
  {
    name: 'Only author',
    data: {
      actualModName: null,
      author: 'Felixandre',
      actualDescription: null
    }
  },
  {
    name: 'With actual description',
    data: {
      actualModName: 'Test Mod',
      author: 'Test Author',
      actualDescription: 'This is a custom description from StringTable.'
    }
  },
  {
    name: 'No metadata',
    data: {
      actualModName: null,
      author: null,
      actualDescription: null
    }
  }
];

console.log('🧪 Testing Improved Mod Description Logic\n');

testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.name}:`);
  console.log(`   Input: modName="${testCase.data.actualModName}", author="${testCase.data.author}"`);
  console.log(`   Output: "${getModDescription(testCase.data)}"`);
  console.log();
});
