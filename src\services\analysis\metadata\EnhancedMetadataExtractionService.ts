/**
 * Enhanced Metadata Extraction Service
 * 
 * Main orchestrator for multi-source metadata extraction.
 * Coordinates all extractors and provides unified interface.
 */

import { Package } from '@s4tk/models';
import { BinaryResourceType } from '@s4tk/models/enums';
import { DEFAULT_METADATA_CONFIG, type MetadataExtractionConfig } from '../../../config/metadata-extraction.config';
import { PackageParserService } from '../../shared/PackageParserService';

import { FilenameMetadataExtractor } from './FilenameMetadataExtractor';
import { CustomStringTableParser } from './CustomStringTableParser';
import { TuningMetadataExtractor } from './TuningMetadataExtractor';
import { SimDataMetadataExtractor } from './SimDataMetadataExtractor';
import { ManifestDetector } from './ManifestDetector';
import { MetadataAggregator, type AggregatedMetadata, type MetadataSource } from './MetadataAggregator';

export interface EnhancedMetadataExtractionOptions {
    readonly config?: Partial<MetadataExtractionConfig>;
    readonly enabledExtractors?: {
        readonly filename?: boolean;
        readonly stringTable?: boolean;
        readonly tuning?: boolean;
        readonly simData?: boolean;
        readonly manifest?: boolean;
    };
    readonly maxProcessingTime?: number;
    readonly enableCaching?: boolean;
}

export interface ExtractionResult {
    readonly metadata: AggregatedMetadata;
    readonly extractionDetails: {
        readonly filename: { attempted: boolean; successful: boolean; error?: string };
        readonly stringTable: { attempted: boolean; successful: boolean; resourceCount: number; error?: string };
        readonly tuning: { attempted: boolean; successful: boolean; resourceCount: number; error?: string };
        readonly simData: { attempted: boolean; successful: boolean; resourceCount: number; error?: string };
        readonly manifest: { attempted: boolean; successful: boolean; manifestCount: number; error?: string };
    };
    readonly performance: {
        readonly totalTime: number;
        readonly extractorTimes: Record<string, number>;
        readonly aggregationTime: number;
    };
}

/**
 * Enhanced metadata extraction service
 */
export class EnhancedMetadataExtractionService {
    private readonly config: MetadataExtractionConfig;
    private readonly filenameExtractor: FilenameMetadataExtractor;
    private readonly stringTableParser: CustomStringTableParser;
    private readonly tuningExtractor: TuningMetadataExtractor;
    private readonly simDataExtractor: SimDataMetadataExtractor;
    private readonly manifestDetector: ManifestDetector;
    private readonly aggregator: MetadataAggregator;
    
    constructor(options: EnhancedMetadataExtractionOptions = {}) {
        this.config = {
            ...DEFAULT_METADATA_CONFIG,
            ...options.config
        };
        
        // Initialize extractors with shared config
        this.filenameExtractor = new FilenameMetadataExtractor({ config: this.config });
        this.stringTableParser = new CustomStringTableParser(this.config);
        this.tuningExtractor = new TuningMetadataExtractor(this.config);
        this.simDataExtractor = new SimDataMetadataExtractor(this.config);
        this.manifestDetector = new ManifestDetector(this.config);
        this.aggregator = new MetadataAggregator(this.config);
    }
    
    /**
     * Extracts metadata from package file
     */
    async extractMetadata(
        packageBuffer: Buffer,
        filename: string,
        options: EnhancedMetadataExtractionOptions = {}
    ): Promise<ExtractionResult> {
        const startTime = performance.now();
        const enabledExtractors = {
            filename: true,
            stringTable: true,
            tuning: true,
            simData: true,
            manifest: true,
            ...options.enabledExtractors
        };
        
        const extractionDetails: ExtractionResult['extractionDetails'] = {
            filename: { attempted: false, successful: false },
            stringTable: { attempted: false, successful: false, resourceCount: 0 },
            tuning: { attempted: false, successful: false, resourceCount: 0 },
            simData: { attempted: false, successful: false, resourceCount: 0 },
            manifest: { attempted: false, successful: false, manifestCount: 0 }
        };
        
        const extractorTimes: Record<string, number> = {};
        const sources: MetadataSource[] = [];
        
        try {
            // Parse package using shared service
            const parseResult = PackageParserService.parsePackage(packageBuffer, filename, {
                enableCaching: true,
                decompressBuffers: true
            });
            const s4tkPackage = parseResult.package;
            
            // 1. Filename extraction
            if (enabledExtractors.filename) {
                const filenameStart = performance.now();
                extractionDetails.filename.attempted = true;
                
                try {
                    const filenameMetadata = await this.filenameExtractor.extractMetadata(filename);
                    if (filenameMetadata) {
                        sources.push(filenameMetadata);
                        extractionDetails.filename.successful = true;
                    }
                } catch (error) {
                    extractionDetails.filename.error = error.message;
                }
                
                extractorTimes.filename = performance.now() - filenameStart;
            }
            
            // 2. StringTable extraction
            if (enabledExtractors.stringTable) {
                const stblStart = performance.now();
                extractionDetails.stringTable.attempted = true;
                
                try {
                    const stblSources = await this.extractFromStringTables(s4tkPackage);
                    sources.push(...stblSources);
                    extractionDetails.stringTable.resourceCount = stblSources.length;
                    extractionDetails.stringTable.successful = stblSources.length > 0;
                } catch (error) {
                    extractionDetails.stringTable.error = error.message;
                }
                
                extractorTimes.stringTable = performance.now() - stblStart;
            }
            
            // 3. Tuning extraction
            if (enabledExtractors.tuning) {
                const tuningStart = performance.now();
                extractionDetails.tuning.attempted = true;
                
                try {
                    const tuningSources = await this.extractFromTuningResources(s4tkPackage);
                    sources.push(...tuningSources);
                    extractionDetails.tuning.resourceCount = tuningSources.length;
                    extractionDetails.tuning.successful = tuningSources.length > 0;
                } catch (error) {
                    extractionDetails.tuning.error = error.message;
                }
                
                extractorTimes.tuning = performance.now() - tuningStart;
            }
            
            // 4. SimData extraction
            if (enabledExtractors.simData) {
                const simDataStart = performance.now();
                extractionDetails.simData.attempted = true;
                
                try {
                    const simDataSources = await this.extractFromSimDataResources(s4tkPackage);
                    sources.push(...simDataSources);
                    extractionDetails.simData.resourceCount = simDataSources.length;
                    extractionDetails.simData.successful = simDataSources.length > 0;
                } catch (error) {
                    extractionDetails.simData.error = error.message;
                }
                
                extractorTimes.simData = performance.now() - simDataStart;
            }
            
            // 5. Manifest detection
            if (enabledExtractors.manifest) {
                const manifestStart = performance.now();
                extractionDetails.manifest.attempted = true;
                
                try {
                    const manifestSources = await this.extractFromManifests(s4tkPackage);
                    sources.push(...manifestSources);
                    extractionDetails.manifest.manifestCount = manifestSources.length;
                    extractionDetails.manifest.successful = manifestSources.length > 0;
                } catch (error) {
                    extractionDetails.manifest.error = error.message;
                }
                
                extractorTimes.manifest = performance.now() - manifestStart;
            }
            
            // Aggregate all sources
            const aggregationStart = performance.now();
            const metadata = await this.aggregator.aggregateMetadata(sources);
            const aggregationTime = performance.now() - aggregationStart;
            
            const totalTime = performance.now() - startTime;
            
            return {
                metadata,
                extractionDetails,
                performance: {
                    totalTime,
                    extractorTimes,
                    aggregationTime
                }
            };
            
        } catch (error) {
            console.error('[EnhancedMetadataExtractionService] Extraction failed:', error);
            
            // Return empty result with error details
            const emptyMetadata = await this.aggregator.aggregateMetadata([]);
            
            return {
                metadata: emptyMetadata,
                extractionDetails,
                performance: {
                    totalTime: performance.now() - startTime,
                    extractorTimes,
                    aggregationTime: 0
                }
            };
        }
    }
    
    /**
     * Extracts metadata from StringTable resources
     */
    private async extractFromStringTables(s4tkPackage: Package): Promise<MetadataSource[]> {
        const sources: MetadataSource[] = [];

        console.log(`[DEBUG] Package has ${s4tkPackage.size} total resources`);

        // Debug: Check all resource types
        const resourceTypes = new Map<number, number>();
        for (const entry of s4tkPackage.entries.values()) {
            const type = entry.key.type;
            resourceTypes.set(type, (resourceTypes.get(type) || 0) + 1);
        }

        console.log('[DEBUG] Resource types found:');
        for (const [type, count] of resourceTypes.entries()) {
            const hexType = '0x' + type.toString(16).toUpperCase().padStart(8, '0');
            console.log(`  ${hexType}: ${count} resources`);
        }

        const stblResources = Array.from(s4tkPackage.entries.values())
            .filter(entry => entry.key.type === BinaryResourceType.StringTable);

        console.log(`[DEBUG] Found ${stblResources.length} StringTable resources (looking for type 0x${BinaryResourceType.StringTable.toString(16).toUpperCase()})`);

        for (let i = 0; i < stblResources.length; i++) {
            const entry = stblResources[i];
            console.log(`[DEBUG] Processing StringTable ${i + 1}/${stblResources.length}`);
            console.log(`[DEBUG] Entry ID: ${entry.id}`);
            console.log(`[DEBUG] Entry value type: ${typeof entry.value}`);
            console.log(`[DEBUG] Entry value constructor: ${entry.value?.constructor?.name}`);
            console.log(`[DEBUG] Has entries property: ${'entries' in (entry.value || {})}`);

            try {
                // Check if entry.value is already a parsed StringTableResource
                if (entry.value && typeof entry.value === 'object' && 'entries' in entry.value) {
                    console.log(`[DEBUG] Using parsed StringTableResource`);
                    // Use the already parsed StringTableResource
                    const stringTable = entry.value as any; // S4TK StringTableResource
                    const metadata = await this.extractFromParsedStringTable(stringTable);
                    if (metadata) {
                        console.log(`[DEBUG] Successfully extracted metadata from parsed StringTable`);
                        sources.push(metadata);
                    } else {
                        console.log(`[DEBUG] No metadata extracted from parsed StringTable`);
                    }
                } else {
                    console.log(`[DEBUG] Falling back to custom parsing`);
                    // Fallback to custom parsing if needed
                    const buffer = Buffer.from(entry.value as any);
                    const metadata = await this.stringTableParser.parseStringTable(buffer);
                    if (metadata) {
                        console.log(`[DEBUG] Successfully extracted metadata from custom parser`);
                        sources.push(metadata);
                    } else {
                        console.log(`[DEBUG] No metadata extracted from custom parser`);
                    }
                }
            } catch (error) {
                console.warn(`[DEBUG] StringTable ${i + 1} parsing failed:`, error.message);
            }
        }

        return sources;
    }
    
    /**
     * Extracts metadata from Tuning resources
     */
    private async extractFromTuningResources(s4tkPackage: Package): Promise<MetadataSource[]> {
        const sources: MetadataSource[] = [];
        
        // Look for XML tuning resources
        const tuningResources = Array.from(s4tkPackage.entries.values())
            .filter(entry => {
                // Common tuning resource types
                return entry.key.type === 0x62E94D38 || // Tuning
                       entry.key.type === 0x545AC67A || // XML
                       entry.key.type === 0x0333406C;   // SimData (XML format)
            });
        
        for (const entry of tuningResources) {
            try {
                const xmlContent = entry.value.toString();
                const metadata = await this.tuningExtractor.extractMetadata(xmlContent, entry.id?.toString());
                if (metadata) {
                    sources.push(metadata);
                }
            } catch (error) {
                console.warn('[EnhancedMetadataExtractionService] Tuning parsing failed:', error);
            }
        }
        
        return sources;
    }
    
    /**
     * Extracts metadata from SimData resources
     */
    private async extractFromSimDataResources(s4tkPackage: Package): Promise<MetadataSource[]> {
        const sources: MetadataSource[] = [];
        
        const simDataResources = Array.from(s4tkPackage.entries.values())
            .filter(entry => entry.key.type === 0x545AC67A); // SimData type
        
        for (const entry of simDataResources) {
            try {
                const buffer = Buffer.from(entry.value as any);
                const metadata = await this.simDataExtractor.extractMetadata(buffer);
                if (metadata) {
                    sources.push(metadata);
                }
            } catch (error) {
                console.warn('[EnhancedMetadataExtractionService] SimData parsing failed:', error);
            }
        }
        
        return sources;
    }
    
    /**
     * Extracts metadata from manifests
     */
    private async extractFromManifests(s4tkPackage: Package): Promise<MetadataSource[]> {
        const sources: MetadataSource[] = [];
        
        // Collect all resources that might contain manifests
        const resourceMap = new Map<string, Buffer>();
        
        for (const entry of s4tkPackage.entries.values()) {
            try {
                const buffer = Buffer.from(entry.value as any);
                resourceMap.set(entry.id?.toString() || 'unknown', buffer);
            } catch (error) {
                // Skip resources that can't be converted to buffer
            }
        }
        
        // Detect manifests
        const manifests = await this.manifestDetector.detectManifests(resourceMap);
        
        // Parse detected manifests
        for (const manifest of manifests) {
            try {
                const metadata = await this.manifestDetector.parseManifest(manifest);
                if (metadata) {
                    sources.push(metadata);
                }
            } catch (error) {
                console.warn('[EnhancedMetadataExtractionService] Manifest parsing failed:', error);
            }
        }
        
        return sources;
    }

    /**
     * Extracts metadata from already-parsed StringTable resource
     */
    private async extractFromParsedStringTable(stringTable: any): Promise<import('./MetadataAggregator').MetadataSource | null> {
        const startTime = performance.now();

        try {
            console.log(`[DEBUG] extractFromParsedStringTable called`);
            console.log(`[DEBUG] stringTable exists: ${!!stringTable}`);
            console.log(`[DEBUG] stringTable.entries exists: ${!!stringTable?.entries}`);
            console.log(`[DEBUG] stringTable.entries type: ${typeof stringTable?.entries}`);
            console.log(`[DEBUG] stringTable.entries constructor: ${stringTable?.entries?.constructor?.name}`);

            if (!stringTable || !stringTable.entries) {
                console.log(`[DEBUG] Early return: missing stringTable or entries`);
                return null;
            }

            // Extract metadata using the same logic as CustomStringTableParser
            const metadata: {
                modName?: string;
                description?: string;
                author?: string;
                confidence: number;
            } = {
                confidence: 20 // Base confidence for successful parsing
            };

            // Convert S4TK entries to our format for analysis
            const stringEntries: Array<{ key: string; value: string }> = [];

            // S4TK StringTableResource has entries as an iterable (like the existing StringTableProcessor)
            try {
                console.log(`[DEBUG] Processing entries with for...of iteration (like existing StringTableProcessor)`);
                let entryCount = 0;
                for (const [key, value] of stringTable.entries) {
                    entryCount++;
                    if (entryCount <= 3) { // Log first 3 entries
                        console.log(`[DEBUG] Entry ${entryCount}: key="${key}" (${typeof key}), value="${value}" (${typeof value})`);
                    }
                    if (typeof value === 'string' && value.length > 0) {
                        // Convert key to string (might be hash or string)
                        const keyStr = typeof key === 'string' ? key : key.toString();
                        stringEntries.push({ key: keyStr, value });
                    }
                }
                console.log(`[DEBUG] Processed ${entryCount} total entries, ${stringEntries.length} valid entries`);
            } catch (iterationError) {
                console.log(`[DEBUG] for...of iteration failed: ${iterationError.message}`);
                console.log(`[DEBUG] Trying alternative access methods`);

                // Fallback: try different access patterns
                if (stringTable.entries && Array.isArray(stringTable.entries)) {
                    console.log(`[DEBUG] Trying Array access with ${stringTable.entries.length} entries`);
                    for (let i = 0; i < Math.min(3, stringTable.entries.length); i++) {
                        const entry = stringTable.entries[i];
                        console.log(`[DEBUG] Array entry ${i}:`, {
                            type: typeof entry,
                            constructor: entry?.constructor?.name,
                            keys: entry && typeof entry === 'object' ? Object.keys(entry) : 'N/A',
                            entry: entry
                        });

                        if (entry && typeof entry === 'object') {
                            // StringEntry objects have _key and value properties
                            const key = entry._key || entry.key || entry.hash || entry.id || i.toString();
                            const value = entry.value || entry.text || entry.string;
                            console.log(`[DEBUG] Extracted key="${key}", value="${value}" (value type: ${typeof value})`);
                            if (typeof value === 'string' && value.length > 0) {
                                stringEntries.push({ key: key.toString(), value });
                            }
                        }
                    }
                    console.log(`[DEBUG] Array access processed ${stringTable.entries.length} entries, found ${stringEntries.length} valid strings`);
                }
            }

            // Extract metadata using pattern matching and content analysis
            metadata.modName = this.extractByPatterns(stringEntries, 'modName');
            metadata.description = this.extractByPatterns(stringEntries, 'description');
            metadata.author = this.extractByPatterns(stringEntries, 'author');

            // Content-based extraction if pattern matching fails
            if (!metadata.modName || !metadata.description || !metadata.author) {
                const contentBased = this.extractByContent(stringEntries);
                if (!metadata.modName && contentBased.modName) metadata.modName = contentBased.modName;
                if (!metadata.description && contentBased.description) metadata.description = contentBased.description;
                if (!metadata.author && contentBased.author) metadata.author = contentBased.author;
            }

            // Calculate confidence
            if (metadata.modName) metadata.confidence += 30;
            if (metadata.description) metadata.confidence += 25;
            if (metadata.author) metadata.confidence += 20;
            if (stringEntries.length > 10) metadata.confidence += 10;

            metadata.confidence = Math.min(metadata.confidence, 100);

            const processingTime = performance.now() - startTime;

            // Only return if we found meaningful metadata
            if (metadata.modName || metadata.description || metadata.author) {
                return {
                    ...metadata,
                    source: 'stringtable' as const,
                    version: stringTable.version || 5,
                    entryCount: stringEntries.length,
                    locale: stringTable.locale || 'unknown',
                    processingTime
                };
            }

            return null;

        } catch (error) {
            console.warn('[EnhancedMetadataExtractionService] Parsed StringTable extraction failed:', error);
            return null;
        }
    }

    /**
     * Extract metadata by pattern matching
     */
    private extractByPatterns(entries: Array<{ key: string; value: string }>, type: 'modName' | 'description' | 'author'): string | undefined {
        const patterns = this.config.stringTable.keyPatterns[type];

        for (const entry of entries) {
            for (const pattern of patterns) {
                if (pattern.test(entry.key) && this.isValidMetadataValue(entry.value, type)) {
                    return this.sanitizeMetadataValue(entry.value);
                }
            }
        }
        return undefined;
    }

    /**
     * Extract metadata by content analysis
     */
    private extractByContent(entries: Array<{ key: string; value: string }>): {
        modName?: string;
        description?: string;
        author?: string;
    } {
        const result: { modName?: string; description?: string; author?: string } = {};

        for (const entry of entries) {
            const value = entry.value.trim();

            if (value.length < 3 || value.length > 200) continue;
            if (/^[A-F0-9]{8,}$/i.test(value) || /^\d+$/.test(value)) continue;

            if (!result.modName && this.looksLikeModName(value)) {
                result.modName = this.sanitizeMetadataValue(value);
            }
            if (!result.description && this.looksLikeDescription(value)) {
                result.description = this.sanitizeMetadataValue(value);
            }
            if (!result.author && this.looksLikeAuthor(value)) {
                result.author = this.sanitizeMetadataValue(value);
            }
        }

        return result;
    }

    private looksLikeModName(value: string): boolean {
        return value.length >= 5 && value.length <= 50 && /[a-zA-Z]/.test(value) && !value.includes('\n') && !/^[a-z\s]+$/.test(value);
    }

    private looksLikeDescription(value: string): boolean {
        return value.length >= 20 && value.length <= 500 && /[a-zA-Z]/.test(value) && (value.includes(' ') || value.includes('.'));
    }

    private looksLikeAuthor(value: string): boolean {
        return value.length >= 2 && value.length <= 30 && /[a-zA-Z]/.test(value) && !value.includes('\n') && !/\d{4,}/.test(value);
    }

    private isValidMetadataValue(value: string, type: string): boolean {
        return value && typeof value === 'string' && value.trim().length > 0 && value.length <= this.config.stringTable.maxStringLength;
    }

    private sanitizeMetadataValue(value: string): string {
        return value.trim().replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '').substring(0, this.config.stringTable.maxStringLength);
    }
}
