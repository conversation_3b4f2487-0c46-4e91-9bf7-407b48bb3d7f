/**
 * Unified Resource Type Registry
 * 
 * Consolidates all resource type definitions to eliminate duplication.
 * Provides organized groups of resource types for different analysis purposes.
 */

import { BinaryResourceType } from '@s4tk/models/enums';

/**
 * Organized resource type groups for different analysis purposes
 */
export const RESOURCE_GROUPS = {
    // Image and texture resources
    TEXTURE_RESOURCES: [
        BinaryResourceType.DdsImage,
        BinaryResourceType.DstImage,
        BinaryResourceType.PngImage,
        BinaryResourceType.RlesImage,
        BinaryResourceType.Rle2Image
    ],

    // Thumbnail and preview resources
    THUMBNAIL_RESOURCES: [
        BinaryResourceType.CasPartThumbnail,
        BinaryResourceType.PngImage,
        BinaryResourceType.DdsImage
    ],

    // Create-a-Sim related resources
    CAS_RESOURCES: [
        BinaryResourceType.CasPart,
        BinaryResourceType.CasPartThumbnail,
        BinaryResourceType.CasModifier,
        BinaryResourceType.CasPreset
    ],

    // 3D mesh and model resources
    MESH_RESOURCES: [
        BinaryResourceType.Model,
        BinaryResourceType.ModelLod,
        BinaryResourceType.Rig,
        BinaryResourceType.BlendGeometry,
        BinaryResourceType.Footprint
    ],

    // Object and build/buy resources
    OBJECT_RESOURCES: [
        BinaryResourceType.ObjectDefinition,
        BinaryResourceType.ObjectCatalog,
        BinaryResourceType.ObjectTuning,
        BinaryResourceType.SimData
    ],

    // Critical resources that indicate mod functionality
    CRITICAL_RESOURCES: [
        BinaryResourceType.CasPart,
        BinaryResourceType.ObjectDefinition,
        BinaryResourceType.SimData,
        BinaryResourceType.CombinedTuning,
        BinaryResourceType.Tuning
    ],

    // Script and code resources
    SCRIPT_RESOURCES: [
        BinaryResourceType.PythonBytecode,
        BinaryResourceType.Script
    ],

    // Tuning and configuration resources
    TUNING_RESOURCES: [
        BinaryResourceType.Tuning,
        BinaryResourceType.CombinedTuning,
        BinaryResourceType.TuningMarkup,
        BinaryResourceType.SimData
    ],

    // Audio resources
    AUDIO_RESOURCES: [
        BinaryResourceType.Audio,
        BinaryResourceType.AudioTuning
    ],

    // Animation resources
    ANIMATION_RESOURCES: [
        BinaryResourceType.Animation,
        BinaryResourceType.AnimationStateMachine,
        BinaryResourceType.Rig
    ],

    // Lot and world resources
    LOT_RESOURCES: [
        BinaryResourceType.LotTuning,
        BinaryResourceType.Footprint,
        BinaryResourceType.ObjectDefinition
    ],

    // UI and interface resources
    UI_RESOURCES: [
        BinaryResourceType.UiData,
        BinaryResourceType.Layout,
        BinaryResourceType.Style
    ]
} as const;

/**
 * Resource type categories for analysis
 */
export const RESOURCE_CATEGORIES = {
    VISUAL: [
        ...RESOURCE_GROUPS.TEXTURE_RESOURCES,
        ...RESOURCE_GROUPS.THUMBNAIL_RESOURCES,
        ...RESOURCE_GROUPS.MESH_RESOURCES
    ],

    GAMEPLAY: [
        ...RESOURCE_GROUPS.TUNING_RESOURCES,
        ...RESOURCE_GROUPS.SCRIPT_RESOURCES,
        ...RESOURCE_GROUPS.OBJECT_RESOURCES
    ],

    CONTENT: [
        ...RESOURCE_GROUPS.CAS_RESOURCES,
        ...RESOURCE_GROUPS.OBJECT_RESOURCES,
        ...RESOURCE_GROUPS.ANIMATION_RESOURCES
    ]
} as const;

/**
 * Resource type priorities for analysis (higher number = higher priority)
 */
export const RESOURCE_PRIORITIES = new Map<number, number>([
    // Critical gameplay resources
    [BinaryResourceType.CasPart, 100],
    [BinaryResourceType.ObjectDefinition, 100],
    [BinaryResourceType.SimData, 95],
    [BinaryResourceType.CombinedTuning, 90],
    
    // Visual resources
    [BinaryResourceType.CasPartThumbnail, 85],
    [BinaryResourceType.Model, 80],
    [BinaryResourceType.DdsImage, 75],
    [BinaryResourceType.PngImage, 70],
    
    // Supporting resources
    [BinaryResourceType.Tuning, 60],
    [BinaryResourceType.Animation, 50],
    [BinaryResourceType.Audio, 40],
    
    // Low priority
    [BinaryResourceType.Layout, 20],
    [BinaryResourceType.Style, 10]
]);

/**
 * Resource type descriptions for UI display
 */
export const RESOURCE_DESCRIPTIONS = new Map<number, string>([
    [BinaryResourceType.CasPart, 'Create-a-Sim Part'],
    [BinaryResourceType.CasPartThumbnail, 'CAS Thumbnail'],
    [BinaryResourceType.ObjectDefinition, 'Object Definition'],
    [BinaryResourceType.SimData, 'Sim Data'],
    [BinaryResourceType.DdsImage, 'DDS Texture'],
    [BinaryResourceType.PngImage, 'PNG Image'],
    [BinaryResourceType.Model, '3D Model'],
    [BinaryResourceType.ModelLod, 'Model LOD'],
    [BinaryResourceType.Rig, 'Animation Rig'],
    [BinaryResourceType.Tuning, 'Tuning Data'],
    [BinaryResourceType.CombinedTuning, 'Combined Tuning'],
    [BinaryResourceType.Animation, 'Animation'],
    [BinaryResourceType.Audio, 'Audio'],
    [BinaryResourceType.Script, 'Script'],
    [BinaryResourceType.PythonBytecode, 'Python Bytecode']
]);

/**
 * Utility functions for resource type operations
 */
export class ResourceTypeUtils {
    /**
     * Checks if a resource type belongs to a specific group
     */
    public static isInGroup(resourceType: number, groupName: keyof typeof RESOURCE_GROUPS): boolean {
        return RESOURCE_GROUPS[groupName].includes(resourceType);
    }

    /**
     * Gets all resource types in multiple groups
     */
    public static getTypesInGroups(groupNames: (keyof typeof RESOURCE_GROUPS)[]): number[] {
        const types = new Set<number>();
        for (const groupName of groupNames) {
            for (const type of RESOURCE_GROUPS[groupName]) {
                types.add(type);
            }
        }
        return Array.from(types);
    }

    /**
     * Gets the priority of a resource type
     */
    public static getPriority(resourceType: number): number {
        return RESOURCE_PRIORITIES.get(resourceType) || 0;
    }

    /**
     * Gets the description of a resource type
     */
    public static getDescription(resourceType: number): string {
        return RESOURCE_DESCRIPTIONS.get(resourceType) || `Unknown Resource (0x${resourceType.toString(16).toUpperCase()})`;
    }

    /**
     * Sorts resource types by priority (highest first)
     */
    public static sortByPriority(resourceTypes: number[]): number[] {
        return resourceTypes.sort((a, b) => this.getPriority(b) - this.getPriority(a));
    }

    /**
     * Filters resource types by category
     */
    public static filterByCategory(
        resourceTypes: number[], 
        category: keyof typeof RESOURCE_CATEGORIES
    ): number[] {
        const categoryTypes = new Set(RESOURCE_CATEGORIES[category]);
        return resourceTypes.filter(type => categoryTypes.has(type));
    }

    /**
     * Gets resource types that are commonly found together
     */
    public static getRelatedTypes(resourceType: number): number[] {
        // CAS parts are often with thumbnails and textures
        if (resourceType === BinaryResourceType.CasPart) {
            return [
                BinaryResourceType.CasPartThumbnail,
                BinaryResourceType.DdsImage,
                BinaryResourceType.SimData
            ];
        }

        // Objects are often with definitions and tuning
        if (resourceType === BinaryResourceType.ObjectDefinition) {
            return [
                BinaryResourceType.SimData,
                BinaryResourceType.Tuning,
                BinaryResourceType.Model,
                BinaryResourceType.DdsImage
            ];
        }

        // Models are often with textures and rigs
        if (resourceType === BinaryResourceType.Model) {
            return [
                BinaryResourceType.DdsImage,
                BinaryResourceType.Rig,
                BinaryResourceType.ModelLod
            ];
        }

        return [];
    }

    /**
     * Determines if a resource type indicates custom content
     */
    public static isCustomContentIndicator(resourceType: number): boolean {
        return [
            BinaryResourceType.CasPart,
            BinaryResourceType.ObjectDefinition,
            BinaryResourceType.Model,
            BinaryResourceType.DdsImage
        ].includes(resourceType);
    }

    /**
     * Determines if a resource type is essential for mod functionality
     */
    public static isEssential(resourceType: number): boolean {
        return RESOURCE_GROUPS.CRITICAL_RESOURCES.includes(resourceType);
    }
}
