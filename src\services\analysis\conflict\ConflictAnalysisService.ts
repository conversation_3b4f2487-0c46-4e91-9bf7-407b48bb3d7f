import { Package } from '@s4tk/models';
import type { PackageFileReadingOptions } from '@s4tk/models/types';
import { RESOURCE_GROUPS } from '../../../constants/ResourceTypeRegistry';
import { packageManager, hashingService } from '../../s4tk';
import { UnifiedErrorHandler, UnifiedErrorCategory } from '../../shared/UnifiedErrorHandler';
import type { ConflictAnalysisResult } from '../../../types/analysis-results';

/**
 * Service responsible for conflict detection and analysis
 * Focuses on identifying potential mod conflicts using TGI signatures
 */
export class ConflictAnalysisService {

    /**
     * Analyzes multiple files for conflicts using optimized S4TK methods
     */
    public analyzeForConflicts(buffers: { buffer: Buffer; filePath: string }[]): ConflictAnalysisResult[] {
        return buffers.map(({ buffer, filePath }) => {
            try {
                const conflictOptions: PackageFileReadingOptions = {
                    resourceFilter: (type: number) => {
                        return RESOURCE_GROUPS.CRITICAL_RESOURCES.includes(type);
                    },
                    decompressBuffers: true, // Need actual data for conflict comparison
                    loadRaw: false,          // Need parsed resources for analysis
                    limit: 100               // Reasonable limit for conflict detection
                };

                const resourceEntries = Package.extractResources(buffer, conflictOptions);
                const validationIssues: string[] = [];

                const tgiSignatures = resourceEntries.map(entry => {
                    // Validate resource before processing
                    if (!entry.key || typeof entry.key.type !== 'number') {
                        validationIssues.push(`Invalid resource key: ${entry.id}`);
                        return null;
                    }

                    // Use S4TK hashing and formatting
                    const resourceMetadata = hashingService.createResourceMetadata(entry.key);
                    const conflictHash = hashingService.generateConflictHash(
                        entry.key.type, 
                        entry.key.group, 
                        entry.key.instance,
                        entry.value?.buffer?.toString('hex').substring(0, 100) // First 100 chars of content
                    );

                    return {
                        type: entry.key.type,
                        group: entry.key.group,
                        instance: entry.key.instance,
                        hash: conflictHash,
                        formattedKey: resourceMetadata.formattedKey,
                        tgiString: resourceMetadata.tgiString
                    };
                }).filter(Boolean); // Remove null entries

                return {
                    filePath,
                    tgiSignatures,
                    hasConflicts: false, // Will be determined by cross-comparison
                    validationIssues
                };
            } catch (error) {
                const errorInfo = UnifiedErrorHandler.createError(
                    error,
                    'ConflictAnalysisService.analyzeForConflicts',
                    filePath,
                    UnifiedErrorCategory.PACKAGE_LOADING
                );

                return {
                    filePath,
                    tgiSignatures: [],
                    hasConflicts: false,
                    validationIssues: [errorInfo.message]
                };
            }
        });
    }

    /**
     * Analyzes conflicts asynchronously with better performance
     */
    public async analyzeForConflictsAsync(buffers: { buffer: Buffer; filePath: string }[]): Promise<ConflictAnalysisResult[]> {
        const results: ConflictAnalysisResult[] = [];
        
        // Process in batches to avoid overwhelming the system
        const batchSize = 5;
        const batches = this.chunkArray(buffers, batchSize);
        
        for (const batch of batches) {
            const batchPromises = batch.map(async ({ buffer, filePath }) => {
                try {
                    const conflictOptions: PackageFileReadingOptions = {
                        resourceFilter: (type: number) => {
                            return RESOURCE_TYPE_GROUPS.CONFLICT_DETECTION_RESOURCES.includes(type);
                        },
                        decompressBuffers: true,
                        loadRaw: false,
                        limit: 100
                    };

                    const resourceEntries = await packageManager.extractResourcesAsync(buffer, conflictOptions);
                    const validationIssues: string[] = [];

                    const tgiSignatures = resourceEntries.map(entry => {
                        if (!entry.key || typeof entry.key.type !== 'number') {
                            validationIssues.push(`Invalid resource key: ${entry.id}`);
                            return null;
                        }

                        const resourceMetadata = hashingService.createResourceMetadata(entry.key);
                        const conflictHash = hashingService.generateConflictHash(
                            entry.key.type, 
                            entry.key.group, 
                            entry.key.instance,
                            entry.value?.buffer?.toString('hex').substring(0, 100)
                        );

                        return {
                            type: entry.key.type,
                            group: entry.key.group,
                            instance: entry.key.instance,
                            hash: conflictHash,
                            formattedKey: resourceMetadata.formattedKey,
                            tgiString: resourceMetadata.tgiString
                        };
                    }).filter(Boolean);

                    return {
                        filePath,
                        tgiSignatures,
                        hasConflicts: false,
                        validationIssues
                    };
                } catch (error) {
                    const errorInfo = UnifiedErrorHandler.createError(
                        error,
                        'ConflictAnalysisService.analyzeForConflictsAsync',
                        filePath,
                        UnifiedErrorCategory.PACKAGE_LOADING
                    );

                    return {
                        filePath,
                        tgiSignatures: [],
                        hasConflicts: false,
                        validationIssues: [errorInfo.message]
                    };
                }
            });
            
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
        }
        
        // Perform cross-comparison to detect actual conflicts
        this.detectActualConflicts(results);
        
        return results;
    }

    /**
     * Detects actual conflicts by comparing TGI signatures across files
     */
    private detectActualConflicts(results: ConflictAnalysisResult[]): void {
        // Create a map of TGI signatures to files
        const tgiMap = new Map<string, ConflictAnalysisResult[]>();
        
        results.forEach(result => {
            result.tgiSignatures.forEach(signature => {
                const key = `${signature.type}:${signature.group}:${signature.instance}`;
                if (!tgiMap.has(key)) {
                    tgiMap.set(key, []);
                }
                tgiMap.get(key)!.push(result);
            });
        });
        
        // Mark files with conflicts
        tgiMap.forEach((conflictingFiles, tgiKey) => {
            if (conflictingFiles.length > 1) {
                conflictingFiles.forEach(file => {
                    file.hasConflicts = true;
                });
            }
        });
    }

    /**
     * Generates conflict report with detailed information
     */
    public generateConflictReport(results: ConflictAnalysisResult[]): {
        totalFiles: number;
        filesWithConflicts: number;
        conflictDetails: Array<{
            tgiSignature: string;
            conflictingFiles: string[];
            resourceType: string;
        }>;
    } {
        const conflictMap = new Map<string, Set<string>>();
        
        // Build conflict map
        results.forEach(result => {
            if (result.hasConflicts) {
                result.tgiSignatures.forEach(signature => {
                    const key = signature.tgiString || `${signature.type}:${signature.group}:${signature.instance}`;
                    if (!conflictMap.has(key)) {
                        conflictMap.set(key, new Set());
                    }
                    conflictMap.get(key)!.add(result.filePath);
                });
            }
        });
        
        // Generate conflict details
        const conflictDetails = Array.from(conflictMap.entries())
            .filter(([_, files]) => files.size > 1)
            .map(([tgiSignature, files]) => ({
                tgiSignature,
                conflictingFiles: Array.from(files),
                resourceType: this.getResourceTypeName(tgiSignature)
            }));
        
        return {
            totalFiles: results.length,
            filesWithConflicts: results.filter(r => r.hasConflicts).length,
            conflictDetails
        };
    }

    /**
     * Gets resource type name from TGI signature
     */
    private getResourceTypeName(tgiSignature: string): string {
        const parts = tgiSignature.split(':');
        if (parts.length >= 1) {
            const typeHex = parts[0];
            // Convert hex to number and get type name
            try {
                const typeNumber = parseInt(typeHex, 16);
                return `Type_${typeHex}`;
            } catch {
                return 'Unknown';
            }
        }
        return 'Unknown';
    }

    /**
     * Chunks an array into smaller arrays for batch processing
     */
    private chunkArray<T>(array: T[], size: number): T[][] {
        const chunks: T[][] = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }
}

// Export singleton instance
export const conflictAnalysisService = new ConflictAnalysisService();