/**
 * Debug script to examine StringTable content and understand the data structure
 * This will help us improve pattern matching for mod names and descriptions
 */

import * as fs from 'fs';
import * as path from 'path';
import { Package } from '@s4tk/models';
import { StringTableResource } from '@s4tk/models';
import { BinaryResourceType } from '@s4tk/models/enums';

async function debugStringTableContent(): Promise<void> {
    console.log('🔍 Debugging StringTable Content');
    console.log('=' .repeat(60));
    
    const modsPath = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    
    if (!fs.existsSync(modsPath)) {
        console.log('❌ Mods folder not found. Please check the path.');
        return;
    }
    
    const files = fs.readdirSync(modsPath)
        .filter(file => file.endsWith('.package'))
        .slice(0, 5); // Debug first 5 files
    
    console.log(`📁 Examining ${files.length} package files for StringTable content`);
    console.log('');
    
    for (const fileName of files) {
        const filePath = path.join(modsPath, fileName);
        
        try {
            console.log(`🔍 Analyzing: ${fileName}`);
            console.log('-'.repeat(40));
            
            const buffer = fs.readFileSync(filePath);
            const s4tkPackage = Package.from(buffer);
            
            // Find StringTable resources
            const stblResources = Array.from(s4tkPackage.entries.values())
                .filter(entry => entry.key.type === BinaryResourceType.StringTable);
            
            if (stblResources.length === 0) {
                console.log('  ❌ No StringTable resources found');
                console.log('');
                continue;
            }
            
            console.log(`  ✅ Found ${stblResources.length} StringTable resource(s)`);
            
            for (let i = 0; i < stblResources.length; i++) {
                const entry = stblResources[i];
                console.log(`  📋 StringTable ${i + 1}:`);
                
                try {
                    const stringTable = StringTableResource.from(entry.value.buffer);
                    console.log(`     Locale: ${stringTable.locale || 'unknown'}`);
                    console.log(`     Size: ${stringTable.size} entries`);
                    
                    if (stringTable.size > 0) {
                        console.log(`     📝 String entries:`);
                        
                        let entryCount = 0;
                        for (const [key, value] of stringTable.entries) {
                            entryCount++;
                            
                            // Show first 20 entries to understand the structure
                            if (entryCount <= 20) {
                                const truncatedValue = value.length > 100 ? 
                                    value.substring(0, 100) + '...' : value;
                                console.log(`        "${key}" = "${truncatedValue}"`);
                            } else if (entryCount === 21) {
                                console.log(`        ... and ${stringTable.size - 20} more entries`);
                                break;
                            }
                        }
                        
                        // Look for potential mod names (longer, meaningful strings)
                        console.log(`     🎯 Potential mod names/descriptions:`);
                        let foundPotential = false;
                        
                        for (const [key, value] of stringTable.entries) {
                            // Look for strings that could be mod names or descriptions
                            if (value.length >= 5 && 
                                value.length <= 200 &&
                                !/^\d+$/.test(value) && // Not just numbers
                                !/^[A-F0-9]{8,}$/i.test(value) && // Not hex codes
                                /[a-zA-Z]/.test(value) // Contains letters
                            ) {
                                const truncatedValue = value.length > 80 ? 
                                    value.substring(0, 80) + '...' : value;
                                console.log(`        "${key}" = "${truncatedValue}"`);
                                foundPotential = true;
                            }
                        }
                        
                        if (!foundPotential) {
                            console.log(`        (No obvious mod names/descriptions found)`);
                        }
                    }
                    
                } catch (error) {
                    console.log(`     ❌ Error parsing StringTable: ${error.message}`);
                }
                
                console.log('');
            }
            
        } catch (error) {
            console.log(`  ❌ Error analyzing file: ${error.message}`);
            console.log('');
        }
    }
    
    console.log('✅ StringTable Content Debug Complete!');
}

// Run the debug
if (require.main === module) {
    debugStringTableContent().catch(console.error);
}

export { debugStringTableContent };
