/**
 * Debug resource type constants
 */

import { BinaryResourceType } from '@s4tk/models/enums';

function debugResourceTypes(): void {
    console.log('🔍 Debug Resource Types');
    console.log('=' .repeat(40));
    
    console.log('BinaryResourceType.StringTable:', BinaryResourceType.StringTable);
    console.log('BinaryResourceType.StringTable (hex):', '0x' + BinaryResourceType.StringTable.toString(16).toUpperCase());

    console.log('');
    console.log('Expected StringTable type from debug: 0x220557DA');
    console.log('Matches BinaryResourceType?', BinaryResourceType.StringTable === 0x220557DA);
}

// Run the debug
if (require.main === module) {
    debugResourceTypes();
}

export { debugResourceTypes };
