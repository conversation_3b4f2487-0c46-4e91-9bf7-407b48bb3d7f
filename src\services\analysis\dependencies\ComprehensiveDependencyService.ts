/**
 * Comprehensive Dependency Service
 * 
 * Provides comprehensive dependency tracking, validation, and resolution for Sims 4 mods.
 * Detects core mod requirements, missing dependencies, and provides resolution suggestions.
 * 
 * Addresses Reddit request: "missing needed mod (like Lot51's core mod)"
 */

import * as fs from 'fs';
import * as path from 'path';
import { Package } from '@s4tk/models';
import type { ResourceEntry } from '@s4tk/models/types';
import type { DependencyInfo, ModDependency } from '../specialized/common/DependencyAnalyzer';
import { ScriptAnalysisUtils } from '../../shared/ScriptAnalysisUtils';
import { PackageParserService } from '../../shared/PackageParserService';

export interface CoreModInfo {
    id: string;
    name: string;
    author: string;
    description: string;
    detectionPatterns: string[];
    requiredFiles: string[];
    downloadUrl?: string;
    isFramework: boolean;
    dependents: string[]; // Mods that depend on this
    version?: string;
    lastUpdated?: string;
}

export interface DependencyValidationResult {
    modFileName: string;
    modFilePath: string;
    isValid: boolean;
    dependencies: DependencyInfo;
    missingDependencies: MissingDependency[];
    installedDependencies: InstalledDependency[];
    warnings: DependencyWarning[];
    recommendations: DependencyRecommendation[];
    riskAssessment: DependencyRiskAssessment;
}

export interface MissingDependency {
    name: string;
    type: 'core_mod' | 'framework' | 'expansion_pack' | 'game_pack' | 'stuff_pack';
    severity: 'critical' | 'high' | 'medium' | 'low';
    description: string;
    downloadUrl?: string;
    alternativeOptions?: string[];
    impactDescription: string;
}

export interface InstalledDependency {
    name: string;
    version?: string;
    filePath: string;
    isUpToDate: boolean;
    lastModified: number;
}

export interface DependencyWarning {
    type: 'version_mismatch' | 'outdated_dependency' | 'potential_conflict' | 'deprecated';
    severity: 'high' | 'medium' | 'low';
    message: string;
    affectedMods: string[];
    resolution?: string;
}

export interface DependencyRecommendation {
    type: 'install' | 'update' | 'remove' | 'configure';
    priority: 'high' | 'medium' | 'low';
    description: string;
    actionRequired: string;
    downloadUrl?: string;
    estimatedImpact: string;
}

export interface DependencyRiskAssessment {
    overallRisk: 'low' | 'medium' | 'high' | 'critical';
    riskFactors: string[];
    stabilityScore: number; // 0-100
    compatibilityScore: number; // 0-100
    maintenanceScore: number; // 0-100
}

export interface CollectionDependencyAnalysis {
    totalMods: number;
    modsWithDependencies: number;
    missingCoreMods: CoreModInfo[];
    dependencyGraph: Map<string, string[]>;
    circularDependencies: string[][];
    orphanedMods: string[];
    coreModUsage: Map<string, number>;
    recommendations: DependencyRecommendation[];
    overallHealth: 'excellent' | 'good' | 'fair' | 'poor';
}

/**
 * Service for comprehensive dependency tracking and validation
 */
export class ComprehensiveDependencyService {
    private static readonly CORE_MODS_DATABASE: CoreModInfo[] = [
        {
            id: 'lot51_core',
            name: 'Lot 51 Core Library',
            author: 'Lot 51',
            description: 'Core framework for Lot 51 mods including UI Cheats Extension',
            detectionPatterns: ['lot51', 'ui_cheats', 'lot_51'],
            requiredFiles: ['lot51_core.ts4script', 'lot51_core.package'],
            downloadUrl: 'https://www.patreon.com/lot51cc',
            isFramework: true,
            dependents: ['ui_cheats_extension', 'lot51_mods'],
            version: '2.0+',
            lastUpdated: '2024-01-01'
        },
        {
            id: 'sims4communitylib',
            name: 'Sims 4 Community Library',
            author: 'ColonolNutty',
            description: 'Community framework for advanced script mods',
            detectionPatterns: ['sims4communitylib', 's4cl', 'colonolnutty'],
            requiredFiles: ['sims4communitylib.ts4script'],
            downloadUrl: 'https://github.com/ColonolNutty/Sims4CommunityLibrary',
            isFramework: true,
            dependents: ['advanced_script_mods'],
            version: '2.8+',
            lastUpdated: '2024-01-01'
        },
        {
            id: 'mccc',
            name: 'MC Command Center',
            author: 'Deaderpool',
            description: 'Master Controller and story progression mod',
            detectionPatterns: ['mccc', 'mc_command_center', 'deaderpool'],
            requiredFiles: ['mc_cmd_center.ts4script'],
            downloadUrl: 'https://deaderpoolmc.wixsite.com/mccc',
            isFramework: false,
            dependents: ['mccc_modules'],
            version: '2024.1.0+',
            lastUpdated: '2024-01-01'
        },
        {
            id: 'wickedwhims',
            name: 'WickedWhims',
            author: 'TURBODRIVER',
            description: 'Adult content framework and animations',
            detectionPatterns: ['wickedwhims', 'ww_', 'turbodriver'],
            requiredFiles: ['wickedwhims.ts4script'],
            downloadUrl: 'https://wickedwhimsmod.com',
            isFramework: true,
            dependents: ['ww_animations', 'adult_mods'],
            version: '171+',
            lastUpdated: '2024-01-01'
        },
        {
            id: 'basemental',
            name: 'Basemental Drugs',
            author: 'Basemental',
            description: 'Drug and alcohol system framework',
            detectionPatterns: ['basemental', 'drugs', 'alcohol'],
            requiredFiles: ['basemental_drugs.ts4script'],
            downloadUrl: 'https://basementalcc.com',
            isFramework: true,
            dependents: ['basemental_mods'],
            version: '7.17+',
            lastUpdated: '2024-01-01'
        }
    ];

    private static installedMods: Map<string, InstalledDependency> = new Map();
    private static modsDirectory: string = '';

    /**
     * Initializes the dependency service with the mods directory
     */
    public static async initialize(modsDirectory: string): Promise<void> {
        this.modsDirectory = modsDirectory;
        await this.scanInstalledMods();
    }

    /**
     * Validates dependencies for a single mod
     */
    public static async validateModDependencies(
        buffer: Buffer,
        fileName: string,
        filePath: string
    ): Promise<DependencyValidationResult> {
        // Import the existing DependencyAnalyzer
        const { DependencyAnalyzer } = await import('../specialized/common/DependencyAnalyzer');
        
        let dependencies: DependencyInfo;
        
        try {
            if (fileName.toLowerCase().endsWith('.ts4script')) {
                dependencies = DependencyAnalyzer.analyzeScriptDependencies(buffer, fileName);
            } else {
                // Use shared package parser
                const parseResult = PackageParserService.parsePackage(buffer, fileName, {
                    enableCaching: true
                });
                dependencies = DependencyAnalyzer.analyzePackageDependencies(parseResult.package, fileName);
            }
        } catch (error) {
            // Create minimal dependency info if analysis fails
            dependencies = {
                requiredMods: [],
                requiredPacks: [],
                conflicts: [],
                features: [],
                dependencies: [],
                riskLevel: 'medium'
            };
        }

        // Enhance with comprehensive analysis
        const enhancedDependencies = await this.enhanceDependencyAnalysis(dependencies, fileName, buffer);
        
        const missingDependencies = await this.findMissingDependencies(enhancedDependencies);
        const installedDependencies = await this.findInstalledDependencies(enhancedDependencies);
        const warnings = await this.generateDependencyWarnings(enhancedDependencies, installedDependencies);
        const recommendations = await this.generateRecommendations(missingDependencies, warnings);
        const riskAssessment = this.assessDependencyRisk(enhancedDependencies, missingDependencies, warnings);

        return {
            modFileName: fileName,
            modFilePath: filePath,
            isValid: missingDependencies.filter(d => d.severity === 'critical').length === 0,
            dependencies: enhancedDependencies,
            missingDependencies,
            installedDependencies,
            warnings,
            recommendations,
            riskAssessment
        };
    }

    /**
     * Analyzes dependencies for an entire mod collection
     */
    public static async analyzeCollection(
        modFiles: Array<{ buffer: Buffer; fileName: string; filePath: string }>
    ): Promise<CollectionDependencyAnalysis> {
        const validationResults: DependencyValidationResult[] = [];
        
        // Validate each mod
        for (const mod of modFiles) {
            try {
                const result = await this.validateModDependencies(mod.buffer, mod.fileName, mod.filePath);
                validationResults.push(result);
            } catch (error) {
                console.warn(`Failed to validate dependencies for ${mod.fileName}:`, error);
            }
        }

        // Build dependency graph
        const dependencyGraph = this.buildDependencyGraph(validationResults);
        
        // Find circular dependencies
        const circularDependencies = this.findCircularDependencies(dependencyGraph);
        
        // Find orphaned mods
        const orphanedMods = this.findOrphanedMods(validationResults);
        
        // Analyze core mod usage
        const coreModUsage = this.analyzeCoreModUsage(validationResults);
        
        // Find missing core mods
        const missingCoreMods = this.findMissingCoreMods(validationResults);
        
        // Generate collection-level recommendations
        const recommendations = this.generateCollectionRecommendations(
            missingCoreMods,
            circularDependencies,
            orphanedMods
        );
        
        // Assess overall health
        const overallHealth = this.assessCollectionHealth(validationResults, missingCoreMods);

        return {
            totalMods: modFiles.length,
            modsWithDependencies: validationResults.filter(r => r.dependencies.dependencies.length > 0).length,
            missingCoreMods,
            dependencyGraph,
            circularDependencies,
            orphanedMods,
            coreModUsage,
            recommendations,
            overallHealth
        };
    }

    /**
     * Scans the mods directory for installed mods
     */
    private static async scanInstalledMods(): Promise<void> {
        this.installedMods.clear();
        
        if (!fs.existsSync(this.modsDirectory)) {
            return;
        }

        const scanDirectory = (dir: string): void => {
            try {
                const items = fs.readdirSync(dir);
                
                for (const item of items) {
                    const fullPath = path.join(dir, item);
                    const stat = fs.statSync(fullPath);
                    
                    if (stat.isDirectory()) {
                        scanDirectory(fullPath);
                    } else if (item.toLowerCase().endsWith('.package') || item.toLowerCase().endsWith('.ts4script')) {
                        const dependency: InstalledDependency = {
                            name: item,
                            filePath: fullPath,
                            isUpToDate: true, // Would need version checking
                            lastModified: stat.mtime.getTime()
                        };
                        
                        this.installedMods.set(item.toLowerCase(), dependency);
                        
                        // Check for core mod patterns
                        for (const coreMod of this.CORE_MODS_DATABASE) {
                            for (const pattern of coreMod.detectionPatterns) {
                                if (item.toLowerCase().includes(pattern.toLowerCase())) {
                                    dependency.name = coreMod.name;
                                    this.installedMods.set(coreMod.id, dependency);
                                    break;
                                }
                            }
                        }
                    }
                }
            } catch (error) {
                console.warn(`Failed to scan directory ${dir}:`, error);
            }
        };

        scanDirectory(this.modsDirectory);
    }

    /**
     * Enhances dependency analysis with core mod detection
     */
    private static async enhanceDependencyAnalysis(
        dependencies: DependencyInfo,
        fileName: string,
        buffer: Buffer
    ): Promise<DependencyInfo> {
        const enhanced = { ...dependencies };
        
        // Detect core mod dependencies from filename patterns
        const lowerFileName = fileName.toLowerCase();
        for (const coreMod of this.CORE_MODS_DATABASE) {
            for (const pattern of coreMod.detectionPatterns) {
                if (lowerFileName.includes(pattern.toLowerCase())) {
                    if (!enhanced.requiredMods.includes(coreMod.name)) {
                        enhanced.requiredMods.push(coreMod.name);
                        enhanced.dependencies.push({
                            type: 'framework',
                            name: coreMod.name,
                            required: true,
                            source: 'import'
                        });
                    }
                    break;
                }
            }
        }

        // Analyze script content for additional dependencies
        if (fileName.toLowerCase().endsWith('.ts4script')) {
            const content = buffer.toString('utf8');
            this.analyzeScriptContentForDependencies(content, enhanced);
        }

        return enhanced;
    }

    /**
     * Analyzes script content for dependency patterns using shared utilities
     */
    private static analyzeScriptContentForDependencies(content: string, dependencies: DependencyInfo): void {
        // Use shared script analysis utilities
        const frameworkDependencies = ScriptAnalysisUtils.detectFrameworkDependencies(content);

        for (const framework of frameworkDependencies) {
            if (!dependencies.requiredMods.includes(framework.name)) {
                dependencies.requiredMods.push(framework.name);
                dependencies.dependencies.push({
                    type: framework.type,
                    name: framework.name,
                    required: true,
                    source: 'import'
                });
            }
        }
    }

    /**
     * Finds missing dependencies
     */
    private static async findMissingDependencies(dependencies: DependencyInfo): Promise<MissingDependency[]> {
        const missing: MissingDependency[] = [];

        for (const requiredMod of dependencies.requiredMods) {
            const coreMod = this.CORE_MODS_DATABASE.find(cm => cm.name === requiredMod);
            if (coreMod && !this.installedMods.has(coreMod.id)) {
                missing.push({
                    name: coreMod.name,
                    type: 'core_mod',
                    severity: 'critical',
                    description: `${coreMod.name} is required but not installed`,
                    downloadUrl: coreMod.downloadUrl,
                    impactDescription: 'Mod will not function without this dependency'
                });
            }
        }

        return missing;
    }

    /**
     * Finds installed dependencies
     */
    private static async findInstalledDependencies(dependencies: DependencyInfo): Promise<InstalledDependency[]> {
        const installed: InstalledDependency[] = [];

        for (const requiredMod of dependencies.requiredMods) {
            const coreMod = this.CORE_MODS_DATABASE.find(cm => cm.name === requiredMod);
            if (coreMod && this.installedMods.has(coreMod.id)) {
                const installedMod = this.installedMods.get(coreMod.id)!;
                installed.push(installedMod);
            }
        }

        return installed;
    }

    /**
     * Generates dependency warnings
     */
    private static async generateDependencyWarnings(
        dependencies: DependencyInfo,
        installedDependencies: InstalledDependency[]
    ): Promise<DependencyWarning[]> {
        const warnings: DependencyWarning[] = [];

        // Check for high-risk dependencies
        if (dependencies.riskLevel === 'high') {
            warnings.push({
                type: 'potential_conflict',
                severity: 'high',
                message: 'This mod has a high risk of conflicts with other mods',
                affectedMods: [dependencies.requiredMods[0] || 'Unknown'],
                resolution: 'Monitor for conflicts and consider alternatives'
            });
        }

        return warnings;
    }

    /**
     * Generates recommendations based on analysis
     */
    private static async generateRecommendations(
        missingDependencies: MissingDependency[],
        warnings: DependencyWarning[]
    ): Promise<DependencyRecommendation[]> {
        const recommendations: DependencyRecommendation[] = [];

        for (const missing of missingDependencies) {
            recommendations.push({
                type: 'install',
                priority: missing.severity === 'critical' ? 'high' : 'medium',
                description: `Install ${missing.name}`,
                actionRequired: `Download and install ${missing.name} from the official source`,
                downloadUrl: missing.downloadUrl,
                estimatedImpact: 'Required for mod functionality'
            });
        }

        return recommendations;
    }

    /**
     * Assesses dependency risk
     */
    private static assessDependencyRisk(
        dependencies: DependencyInfo,
        missingDependencies: MissingDependency[],
        warnings: DependencyWarning[]
    ): DependencyRiskAssessment {
        const riskFactors: string[] = [];
        let stabilityScore = 100;
        let compatibilityScore = 100;
        let maintenanceScore = 100;

        // Assess missing critical dependencies
        const criticalMissing = missingDependencies.filter(d => d.severity === 'critical').length;
        if (criticalMissing > 0) {
            riskFactors.push(`${criticalMissing} critical dependencies missing`);
            stabilityScore -= criticalMissing * 30;
        }

        // Assess warnings
        const highWarnings = warnings.filter(w => w.severity === 'high').length;
        if (highWarnings > 0) {
            riskFactors.push(`${highWarnings} high-severity warnings`);
            compatibilityScore -= highWarnings * 20;
        }

        // Assess overall risk level
        if (dependencies.riskLevel === 'high') {
            riskFactors.push('High-risk mod type');
            maintenanceScore -= 25;
        }

        const overallRisk = criticalMissing > 0 ? 'critical' :
                           highWarnings > 0 || dependencies.riskLevel === 'high' ? 'high' :
                           missingDependencies.length > 0 ? 'medium' : 'low';

        return {
            overallRisk,
            riskFactors,
            stabilityScore: Math.max(0, stabilityScore),
            compatibilityScore: Math.max(0, compatibilityScore),
            maintenanceScore: Math.max(0, maintenanceScore)
        };
    }

    /**
     * Builds dependency graph for the collection
     */
    private static buildDependencyGraph(results: DependencyValidationResult[]): Map<string, string[]> {
        const graph = new Map<string, string[]>();
        
        for (const result of results) {
            const dependencies = result.dependencies.requiredMods;
            graph.set(result.modFileName, dependencies);
        }
        
        return graph;
    }

    /**
     * Finds circular dependencies
     */
    private static findCircularDependencies(graph: Map<string, string[]>): string[][] {
        // Simplified circular dependency detection
        // In a real implementation, this would use proper graph algorithms
        return [];
    }

    /**
     * Finds orphaned mods (mods with missing dependencies)
     */
    private static findOrphanedMods(results: DependencyValidationResult[]): string[] {
        return results
            .filter(r => r.missingDependencies.some(d => d.severity === 'critical'))
            .map(r => r.modFileName);
    }

    /**
     * Analyzes core mod usage across the collection
     */
    private static analyzeCoreModUsage(results: DependencyValidationResult[]): Map<string, number> {
        const usage = new Map<string, number>();
        
        for (const result of results) {
            for (const requiredMod of result.dependencies.requiredMods) {
                usage.set(requiredMod, (usage.get(requiredMod) || 0) + 1);
            }
        }
        
        return usage;
    }

    /**
     * Finds missing core mods across the collection
     */
    private static findMissingCoreMods(results: DependencyValidationResult[]): CoreModInfo[] {
        const requiredCoreMods = new Set<string>();
        
        for (const result of results) {
            for (const missing of result.missingDependencies) {
                if (missing.type === 'core_mod') {
                    requiredCoreMods.add(missing.name);
                }
            }
        }
        
        return this.CORE_MODS_DATABASE.filter(cm => requiredCoreMods.has(cm.name));
    }

    /**
     * Generates collection-level recommendations
     */
    private static generateCollectionRecommendations(
        missingCoreMods: CoreModInfo[],
        circularDependencies: string[][],
        orphanedMods: string[]
    ): DependencyRecommendation[] {
        const recommendations: DependencyRecommendation[] = [];

        for (const coreMod of missingCoreMods) {
            recommendations.push({
                type: 'install',
                priority: 'high',
                description: `Install ${coreMod.name} to support dependent mods`,
                actionRequired: `Download ${coreMod.name} from ${coreMod.downloadUrl || 'official source'}`,
                downloadUrl: coreMod.downloadUrl,
                estimatedImpact: `Will enable ${coreMod.dependents.length} dependent mods`
            });
        }

        return recommendations;
    }

    /**
     * Assesses overall collection health
     */
    private static assessCollectionHealth(
        results: DependencyValidationResult[],
        missingCoreMods: CoreModInfo[]
    ): 'excellent' | 'good' | 'fair' | 'poor' {
        const totalMods = results.length;
        const validMods = results.filter(r => r.isValid).length;
        const validPercentage = (validMods / totalMods) * 100;

        if (missingCoreMods.length === 0 && validPercentage >= 95) return 'excellent';
        if (missingCoreMods.length <= 1 && validPercentage >= 85) return 'good';
        if (missingCoreMods.length <= 3 && validPercentage >= 70) return 'fair';
        return 'poor';
    }

    /**
     * Gets the core mods database
     */
    public static getCoreModsDatabase(): CoreModInfo[] {
        return [...this.CORE_MODS_DATABASE];
    }

    /**
     * Gets installed mods information
     */
    public static getInstalledMods(): Map<string, InstalledDependency> {
        return new Map(this.installedMods);
    }
}
