/**
 * Debug script to identify why other extractors are failing
 */

import * as fs from 'fs';
import * as path from 'path';
import { Package } from '@s4tk/models';
import { BinaryResourceType } from '@s4tk/models/enums';
import { CustomStringTableParser } from '../services/analysis/metadata/CustomStringTableParser';
import { TuningMetadataExtractor } from '../services/analysis/metadata/TuningMetadataExtractor';
import { SimDataMetadataExtractor } from '../services/analysis/metadata/SimDataMetadataExtractor';

async function debugExtractionFailures(): Promise<void> {
    console.log('🔍 Debugging Extraction Failures');
    console.log('=' .repeat(50));
    
    const modsPath = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    const fileName = '!LittleMsSam_SimDaDatingApp_NoFWord.package'; // Test with a complex mod
    const filePath = path.join(modsPath, fileName);
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ Test file not found');
        return;
    }
    
    try {
        console.log(`🔍 Debugging: ${fileName}`);
        console.log('');
        
        const buffer = fs.readFileSync(filePath);
        const s4tkPackage = Package.from(buffer);
        
        console.log(`📦 Package loaded successfully`);
        console.log(`📊 Total resources: ${s4tkPackage.size}`);
        console.log('');
        
        // Analyze resource types
        const resourceTypes = new Map<number, number>();
        for (const entry of s4tkPackage.entries.values()) {
            const type = entry.key.type;
            resourceTypes.set(type, (resourceTypes.get(type) || 0) + 1);
        }
        
        console.log('📋 Resource Type Breakdown:');
        for (const [type, count] of resourceTypes.entries()) {
            const hexType = '0x' + type.toString(16).toUpperCase().padStart(8, '0');
            let typeName = 'Unknown';
            
            if (type === BinaryResourceType.StringTable) typeName = 'StringTable';
            else if (type === 0x62E94D38) typeName = 'Tuning';
            else if (type === 0x545AC67A) typeName = 'SimData';
            else if (type === 0x0333406C) typeName = 'XML';
            
            console.log(`   ${hexType} (${typeName}): ${count} resources`);
        }
        console.log('');
        
        // Test StringTable extraction
        console.log('🔍 Testing StringTable Extraction:');
        const stblResources = Array.from(s4tkPackage.entries.values())
            .filter(entry => entry.key.type === BinaryResourceType.StringTable);
        
        console.log(`   Found ${stblResources.length} StringTable resources`);
        
        if (stblResources.length > 0) {
            const parser = new CustomStringTableParser();
            
            for (let i = 0; i < Math.min(3, stblResources.length); i++) {
                const entry = stblResources[i];
                console.log(`   Testing STBL ${i + 1}:`);
                
                try {
                    // Check the actual entry structure
                    console.log(`     Entry ID: ${entry.id}`);
                    console.log(`     Entry key type: 0x${entry.key.type.toString(16)}`);
                    console.log(`     Entry value type: ${typeof entry.value}`);
                    console.log(`     Entry value constructor: ${entry.value?.constructor?.name}`);
                    
                    // Try to get buffer
                    let buffer: Buffer | null = null;
                    if (Buffer.isBuffer(entry.value)) {
                        buffer = entry.value;
                    } else if (entry.value && typeof entry.value === 'object' && 'buffer' in entry.value) {
                        buffer = entry.value.buffer as Buffer;
                    } else {
                        console.log(`     ❌ Cannot extract buffer from entry.value`);
                        continue;
                    }
                    
                    console.log(`     Buffer size: ${buffer.length} bytes`);
                    console.log(`     First 16 bytes: ${Array.from(buffer.slice(0, 16)).map(b => b.toString(16).padStart(2, '0')).join(' ')}`);
                    
                    const result = await parser.parseStringTable(buffer);
                    if (result) {
                        console.log(`     ✅ Parsed successfully!`);
                        console.log(`        Mod Name: ${result.modName || 'None'}`);
                        console.log(`        Author: ${result.author || 'None'}`);
                        console.log(`        Description: ${result.description || 'None'}`);
                        console.log(`        Confidence: ${result.confidence}%`);
                        console.log(`        Entry Count: ${result.entryCount}`);
                    } else {
                        console.log(`     ❌ Parsing returned null`);
                    }
                    
                } catch (error) {
                    console.log(`     ❌ Error: ${error.message}`);
                }
            }
        }
        console.log('');
        
        // Test Tuning extraction
        console.log('🔍 Testing Tuning Extraction:');
        const tuningResources = Array.from(s4tkPackage.entries.values())
            .filter(entry => {
                return entry.key.type === 0x62E94D38 || // Tuning
                       entry.key.type === 0x545AC67A || // XML
                       entry.key.type === 0x0333406C;   // SimData (XML format)
            });
        
        console.log(`   Found ${tuningResources.length} Tuning/XML resources`);
        
        if (tuningResources.length > 0) {
            const extractor = new TuningMetadataExtractor();
            
            for (let i = 0; i < Math.min(3, tuningResources.length); i++) {
                const entry = tuningResources[i];
                console.log(`   Testing Tuning ${i + 1}:`);
                
                try {
                    const xmlContent = entry.value.toString();
                    console.log(`     Content length: ${xmlContent.length} chars`);
                    console.log(`     First 200 chars: "${xmlContent.substring(0, 200)}..."`);
                    
                    const result = await extractor.extractMetadata(xmlContent, entry.id?.toString());
                    if (result) {
                        console.log(`     ✅ Extracted metadata!`);
                        console.log(`        Author: ${result.author || 'None'}`);
                        console.log(`        Mod Name: ${result.modName || 'None'}`);
                        console.log(`        Version: ${result.version || 'None'}`);
                        console.log(`        Comments: ${result.comments.length}`);
                        console.log(`        Confidence: ${result.confidence}%`);
                    } else {
                        console.log(`     ❌ No metadata extracted`);
                    }
                    
                } catch (error) {
                    console.log(`     ❌ Error: ${error.message}`);
                }
            }
        }
        console.log('');
        
        // Test SimData extraction
        console.log('🔍 Testing SimData Extraction:');
        const simDataResources = Array.from(s4tkPackage.entries.values())
            .filter(entry => entry.key.type === 0x545AC67A); // SimData type
        
        console.log(`   Found ${simDataResources.length} SimData resources`);
        
        if (simDataResources.length > 0) {
            const extractor = new SimDataMetadataExtractor();
            
            for (let i = 0; i < Math.min(3, simDataResources.length); i++) {
                const entry = simDataResources[i];
                console.log(`   Testing SimData ${i + 1}:`);
                
                try {
                    const buffer = Buffer.from(entry.value as any);
                    console.log(`     Buffer size: ${buffer.length} bytes`);
                    
                    const result = await extractor.extractMetadata(buffer);
                    if (result) {
                        console.log(`     ✅ Extracted metadata!`);
                        console.log(`        Author: ${result.author || 'None'}`);
                        console.log(`        Mod Name: ${result.modName || 'None'}`);
                        console.log(`        Version: ${result.version || 'None'}`);
                        console.log(`        Data Type: ${result.dataType || 'None'}`);
                        console.log(`        Field Count: ${result.fieldCount}`);
                        console.log(`        Confidence: ${result.confidence}%`);
                    } else {
                        console.log(`     ❌ No metadata extracted`);
                    }
                    
                } catch (error) {
                    console.log(`     ❌ Error: ${error.message}`);
                }
            }
        }
        
    } catch (error) {
        console.log(`❌ Package loading failed: ${error.message}`);
    }
}

// Run the debug
if (require.main === module) {
    debugExtractionFailures().catch(console.error);
}

export { debugExtractionFailures };
