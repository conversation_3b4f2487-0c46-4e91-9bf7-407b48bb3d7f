<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <!-- Modal Header -->
      <div class="modal-header">
        <div class="header-content">
          <h2 class="modal-title">{{ displayName }}</h2>
          <p class="modal-subtitle">{{ mod.category || 'Unknown Category' }}</p>
        </div>
        <button @click="$emit('close')" class="close-button">
          <XMarkIcon class="w-6 h-6" />
        </button>
      </div>

      <!-- Modal Body -->
      <div class="modal-body">
        <!-- Thumbnail and Basic Info -->
        <div class="info-section">
          <div class="thumbnail-section">
            <div v-if="thumbnailUrl" class="large-thumbnail">
              <img :src="thumbnailUrl" :alt="`${mod.fileName} thumbnail`" class="thumbnail-image" />
            </div>
            <div v-else class="thumbnail-placeholder">
              <component :is="getPlaceholderIcon()" class="placeholder-icon" />
              <span class="placeholder-text">{{ getPlaceholderText() }}</span>
            </div>
            
            <!-- Action Buttons -->
            <div class="action-buttons">
              <button @click="openInExplorer" class="action-btn primary">
                <FolderOpenIcon class="w-4 h-4" />
                Open in Explorer
              </button>
              <button @click="copyPath" class="action-btn secondary">
                <ClipboardIcon class="w-4 h-4" />
                Copy Path
              </button>
              <button v-if="mod.isCorrupted" @click="deleteMod" class="action-btn danger">
                <TrashIcon class="w-4 h-4" />
                Delete
              </button>
            </div>
          </div>

          <div class="basic-info">
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">File Name:</span>
                <span class="info-value">{{ mod.fileName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">File Size:</span>
                <span class="info-value">{{ formatFileSize(mod.fileSize) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Resources:</span>
                <span class="info-value">{{ mod.resourceCount || 0 }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Last Modified:</span>
                <span class="info-value">{{ formatDate(mod.lastModified) }}</span>
              </div>
              <div v-if="mod.author" class="info-item">
                <span class="info-label">Author:</span>
                <span class="info-value">{{ mod.author }}</span>
              </div>
              <div v-if="mod.version" class="info-item">
                <span class="info-label">Version:</span>
                <span class="info-value">{{ mod.version }}</span>
              </div>
            </div>

            <!-- Status Badges -->
            <div class="status-badges">
              <span v-if="mod.hasConflicts" class="badge badge-warning">
                <ExclamationTriangleIcon class="w-4 h-4" />
                Has Conflicts
              </span>
              <span v-if="mod.isCorrupted" class="badge badge-error">
                <XCircleIcon class="w-4 h-4" />
                Corrupted
              </span>
              <span v-if="isHighQuality" class="badge badge-success">
                <SparklesIcon class="w-4 h-4" />
                High Quality
              </span>
            </div>
          </div>
        </div>

        <!-- Enhanced Content Analysis -->
        <div v-if="hasEnhancedContent" class="enhanced-section">
          <h3 class="section-title">Content Analysis</h3>
          
          <!-- Object Classification -->
          <div v-if="mod.objectClassification" class="classification-card">
            <h4 class="classification-title">Object Classification</h4>
            <div class="classification-content">
              <div class="classification-item">
                <span class="classification-label">Category:</span>
                <span class="classification-value">{{ mod.objectClassification.category }}</span>
              </div>
              <div class="classification-item">
                <span class="classification-label">Specific Type:</span>
                <span class="classification-value">{{ mod.objectClassification.specificType }}</span>
              </div>
              <div v-if="mod.objectClassification.roomAssignment" class="classification-item">
                <span class="classification-label">Room Assignment:</span>
                <span class="classification-value">{{ mod.objectClassification.roomAssignment }}</span>
              </div>
              <div class="classification-item">
                <span class="classification-label">Confidence:</span>
                <div class="confidence-bar">
                  <div 
                    class="confidence-fill" 
                    :style="{ width: `${(mod.objectClassification.confidence * 100)}%` }"
                  ></div>
                  <span class="confidence-text">{{ Math.round(mod.objectClassification.confidence * 100) }}%</span>
                </div>
              </div>
              <div v-if="mod.objectClassification.functionality?.length" class="classification-item">
                <span class="classification-label">Functionality:</span>
                <div class="functionality-tags">
                  <span 
                    v-for="func in mod.objectClassification.functionality" 
                    :key="func" 
                    class="functionality-tag"
                  >
                    {{ func }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- CAS Classification -->
          <div v-if="mod.universalClassification" class="classification-card">
            <h4 class="classification-title">CAS Classification</h4>
            <div class="classification-content">
              <div class="classification-item">
                <span class="classification-label">Category:</span>
                <span class="classification-value">{{ mod.universalClassification.category }}</span>
              </div>
              <div v-if="mod.universalClassification.subcategory" class="classification-item">
                <span class="classification-label">Subcategory:</span>
                <span class="classification-value">{{ mod.universalClassification.subcategory }}</span>
              </div>
              <div class="classification-item">
                <span class="classification-label">Confidence:</span>
                <div class="confidence-bar">
                  <div 
                    class="confidence-fill" 
                    :style="{ width: `${(mod.universalClassification.confidence * 100)}%` }"
                  ></div>
                  <span class="confidence-text">{{ Math.round(mod.universalClassification.confidence * 100) }}%</span>
                </div>
              </div>
              <div v-if="mod.universalClassification.ageGroups?.length" class="classification-item">
                <span class="classification-label">Age Groups:</span>
                <div class="age-group-tags">
                  <span 
                    v-for="age in mod.universalClassification.ageGroups" 
                    :key="age" 
                    class="age-group-tag"
                  >
                    {{ age }}
                  </span>
                </div>
              </div>
              <div v-if="mod.universalClassification.genders?.length" class="classification-item">
                <span class="classification-label">Genders:</span>
                <div class="gender-tags">
                  <span 
                    v-for="gender in mod.universalClassification.genders" 
                    :key="gender" 
                    class="gender-tag"
                  >
                    {{ gender }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Description -->
        <div v-if="mod.description" class="description-section">
          <h3 class="section-title">Description</h3>
          <p class="description-text">{{ mod.description }}</p>
        </div>

        <!-- Resource Breakdown -->
        <div v-if="mod.resourceBreakdown" class="resources-section">
          <h3 class="section-title">Resource Breakdown</h3>
          <div class="resource-grid">
            <div 
              v-for="(count, type) in mod.resourceBreakdown" 
              :key="type" 
              class="resource-item"
            >
              <span class="resource-type">{{ formatResourceType(type) }}:</span>
              <span class="resource-count">{{ count }}</span>
            </div>
          </div>
        </div>

        <!-- Conflicts and Issues -->
        <div v-if="mod.conflicts?.length || mod.issues?.length" class="issues-section">
          <h3 class="section-title">Issues & Conflicts</h3>
          
          <div v-if="mod.conflicts?.length" class="conflicts-list">
            <h4 class="subsection-title">Conflicts</h4>
            <div 
              v-for="conflict in mod.conflicts" 
              :key="conflict.id" 
              class="conflict-item"
            >
              <ExclamationTriangleIcon class="w-4 h-4 text-yellow-500" />
              <span>{{ conflict.description }}</span>
            </div>
          </div>

          <div v-if="mod.issues?.length" class="issues-list">
            <h4 class="subsection-title">Issues</h4>
            <div 
              v-for="issue in mod.issues" 
              :key="issue" 
              class="issue-item"
            >
              <XCircleIcon class="w-4 h-4 text-red-500" />
              <span>{{ issue }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="modal-footer">
        <button @click="$emit('close')" class="footer-btn secondary">
          Close
        </button>
        <button @click="analyzeAgain" class="footer-btn primary">
          <ArrowPathIcon class="w-4 h-4" />
          Re-analyze
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  XMarkIcon,
  FolderOpenIcon,
  ClipboardIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  SparklesIcon,
  ArrowPathIcon,
  DocumentIcon,
  UserIcon,
  HomeIcon,
  CogIcon,
  PuzzlePieceIcon
} from '@heroicons/vue/24/outline';

import type { ModData } from '../../../types/ModData';
import { ThumbnailExtractionService } from '../../../services/visual/ThumbnailExtractionService';

// Props
interface Props {
  mod: ModData;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  close: [];
  delete: [modId: string];
  reanalyze: [modId: string];
}>();

// Reactive state
const thumbnailUrl = ref<string | null>(null);

// Computed properties
const displayName = computed(() => {
  return props.mod.fileName.replace(/\.(package|ts4script)$/i, '').replace(/_/g, ' ');
});

const isHighQuality = computed(() => {
  const hasHighResources = (props.mod.resourceCount || 0) > 50;
  const hasLargeSize = (props.mod.fileSize || 0) > 1024 * 1024;
  const hasEnhancedAnalysis = props.mod.objectClassification?.confidence > 0.8;
  
  return hasHighResources || hasLargeSize || hasEnhancedAnalysis;
});

const hasEnhancedContent = computed(() => {
  return props.mod.objectClassification || props.mod.universalClassification;
});

// Methods
const handleOverlayClick = () => {
  emit('close');
};

const openInExplorer = () => {
  window.electronAPI?.openInExplorer?.(props.mod.filePath);
};

const copyPath = async () => {
  try {
    await navigator.clipboard.writeText(props.mod.filePath);
    // Could show a toast notification here
  } catch (error) {
    console.error('Failed to copy path:', error);
  }
};

const deleteMod = () => {
  if (confirm(`Are you sure you want to delete "${props.mod.fileName}"?`)) {
    emit('delete', props.mod.id);
    emit('close');
  }
};

const analyzeAgain = () => {
  emit('reanalyze', props.mod.id);
};

const getPlaceholderIcon = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas') || category.includes('create-a-sim')) return UserIcon;
  if (category.includes('build') || category.includes('buy')) return HomeIcon;
  if (category.includes('script')) return CogIcon;
  if (category.includes('gameplay')) return PuzzlePieceIcon;
  
  return DocumentIcon;
};

const getPlaceholderText = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas')) return 'CAS Content';
  if (category.includes('build') || category.includes('buy')) return 'Build/Buy Content';
  if (category.includes('script')) return 'Script Mod';
  if (category.includes('gameplay')) return 'Gameplay Mod';
  
  return 'Mod Content';
};

const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

const formatDate = (timestamp: number | undefined): string => {
  if (!timestamp) return 'Unknown';
  
  const date = new Date(timestamp);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

const formatResourceType = (type: string): string => {
  return type.charAt(0).toUpperCase() + type.slice(1).replace(/([A-Z])/g, ' $1');
};

const loadThumbnail = async () => {
  if (props.mod.thumbnailData) {
    thumbnailUrl.value = props.mod.thumbnailData;
    return;
  }
  
  try {
    const fs = await import('fs');
    const buffer = fs.readFileSync(props.mod.filePath);
    
    const result = await ThumbnailExtractionService.extractThumbnails(
      buffer,
      props.mod.fileName,
      {
        maxThumbnails: 1,
        preferredFormat: 'webp',
        maxWidth: 512,
        maxHeight: 512,
        prioritizeCasThumbnails: true
      }
    );
    
    if (result.success && result.thumbnails.length > 0) {
      thumbnailUrl.value = result.thumbnails[0].imageData;
    }
  } catch (error) {
    console.error('Failed to load thumbnail:', error);
  }
};

// Lifecycle
onMounted(() => {
  loadThumbnail();
});
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;
}

.modal-container {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] 
         flex flex-col overflow-hidden;
}

/* Header */
.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.header-content {
  @apply flex-1;
}

.modal-title {
  @apply text-2xl font-bold text-gray-900 dark:text-white;
}

.modal-subtitle {
  @apply text-gray-600 dark:text-gray-400 mt-1;
}

.close-button {
  @apply p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200
         hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors;
}

/* Body */
.modal-body {
  @apply flex-1 overflow-y-auto p-6 space-y-6;
}

.info-section {
  @apply flex space-x-6;
}

.thumbnail-section {
  @apply flex-shrink-0;
}

.large-thumbnail {
  @apply w-64 h-64 rounded-lg overflow-hidden mb-4;
}

.thumbnail-image {
  @apply w-full h-full object-cover;
}

.thumbnail-placeholder {
  @apply w-64 h-64 rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 
         dark:from-gray-700 dark:to-gray-800 flex flex-col items-center justify-center
         text-gray-500 dark:text-gray-400 mb-4;
}

.placeholder-icon {
  @apply w-16 h-16 mb-2;
}

.placeholder-text {
  @apply text-lg font-medium;
}

.action-buttons {
  @apply space-y-2;
}

.action-btn {
  @apply w-full flex items-center justify-center space-x-2 px-4 py-2 rounded-lg
         font-medium transition-colors;
}

.action-btn.primary {
  @apply bg-green-500 text-white hover:bg-green-600;
}

.action-btn.secondary {
  @apply bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300
         hover:bg-gray-300 dark:hover:bg-gray-600;
}

.action-btn.danger {
  @apply bg-red-500 text-white hover:bg-red-600;
}

.basic-info {
  @apply flex-1;
}

.info-grid {
  @apply grid grid-cols-2 gap-4 mb-4;
}

.info-item {
  @apply flex flex-col;
}

.info-label {
  @apply text-sm font-medium text-gray-500 dark:text-gray-400;
}

.info-value {
  @apply text-gray-900 dark:text-white font-medium;
}

.status-badges {
  @apply flex flex-wrap gap-2;
}

.badge {
  @apply flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.badge-error {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

.badge-success {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

/* Enhanced Content */
.enhanced-section {
  @apply space-y-4;
}

.section-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-4;
}

.classification-card {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-4;
}

.classification-title {
  @apply text-lg font-medium text-gray-900 dark:text-white mb-3;
}

.classification-content {
  @apply space-y-3;
}

.classification-item {
  @apply flex items-center justify-between;
}

.classification-label {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
}

.classification-value {
  @apply text-gray-900 dark:text-white font-medium;
}

.confidence-bar {
  @apply relative w-32 h-4 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden;
}

.confidence-fill {
  @apply h-full bg-green-500 transition-all duration-300;
}

.confidence-text {
  @apply absolute inset-0 flex items-center justify-center text-xs font-medium text-white;
}

.functionality-tags, .age-group-tags, .gender-tags {
  @apply flex flex-wrap gap-1;
}

.functionality-tag, .age-group-tag, .gender-tag {
  @apply px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200
         text-xs rounded-full;
}

/* Other sections */
.description-section, .resources-section, .issues-section {
  @apply space-y-3;
}

.description-text {
  @apply text-gray-700 dark:text-gray-300 leading-relaxed;
}

.resource-grid {
  @apply grid grid-cols-3 gap-4;
}

.resource-item {
  @apply flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg;
}

.resource-type {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
}

.resource-count {
  @apply text-lg font-bold text-gray-900 dark:text-white;
}

.subsection-title {
  @apply text-lg font-medium text-gray-900 dark:text-white;
}

.conflicts-list, .issues-list {
  @apply space-y-2;
}

.conflict-item, .issue-item {
  @apply flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg;
}

/* Footer */
.modal-footer {
  @apply flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700;
}

.footer-btn {
  @apply flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors;
}

.footer-btn.primary {
  @apply bg-green-500 text-white hover:bg-green-600;
}

.footer-btn.secondary {
  @apply bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300
         hover:bg-gray-300 dark:hover:bg-gray-600;
}
</style>
