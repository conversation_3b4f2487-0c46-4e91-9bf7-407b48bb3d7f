/**
 * Shared Package Parser Service
 * 
 * Consolidates S4TK Package parsing logic to eliminate duplication across services.
 * Provides optimized parsing with caching and standardized resource extraction.
 */

import { Package } from '@s4tk/models';
import type { ResourceEntry } from '@s4tk/models/types';

export interface PackageParseOptions {
    decompressBuffers?: boolean;
    loadRaw?: boolean;
    resourceFilter?: (type: number, group: number, instance: bigint) => boolean;
    enableCaching?: boolean;
}

export interface PackageParseResult {
    package: Package;
    resourceCount: number;
    parseTime: number;
    fromCache: boolean;
}

export interface ResourceExtractionOptions {
    resourceTypes?: number[];
    includeEmpty?: boolean;
    maxResources?: number;
}

/**
 * Centralized service for S4TK Package operations
 */
export class PackageParserService {
    private static packageCache = new Map<string, { package: Package; timestamp: number }>();
    private static readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
    private static readonly MAX_CACHE_SIZE = 100;

    /**
     * Parses a package buffer with optimized caching
     */
    public static parsePackage(
        buffer: Buffer, 
        fileName: string,
        options: PackageParseOptions = {}
    ): PackageParseResult {
        const startTime = performance.now();
        const opts = {
            decompressBuffers: true,
            loadRaw: false,
            enableCaching: true,
            ...options
        };

        // Check cache first
        if (opts.enableCaching) {
            const cacheKey = this.generateCacheKey(buffer, fileName, opts);
            const cached = this.packageCache.get(cacheKey);
            
            if (cached && (Date.now() - cached.timestamp) < this.CACHE_TTL) {
                return {
                    package: cached.package,
                    resourceCount: cached.package.size,
                    parseTime: performance.now() - startTime,
                    fromCache: true
                };
            }
        }

        // Parse package
        const s4tkPackage = Package.from(buffer, {
            decompressBuffers: opts.decompressBuffers,
            loadRaw: opts.loadRaw,
            resourceFilter: opts.resourceFilter
        });

        const result: PackageParseResult = {
            package: s4tkPackage,
            resourceCount: s4tkPackage.size,
            parseTime: performance.now() - startTime,
            fromCache: false
        };

        // Cache result
        if (opts.enableCaching) {
            this.cachePackage(buffer, fileName, opts, s4tkPackage);
        }

        return result;
    }

    /**
     * Extracts resources by type with optimized filtering
     */
    public static getResourcesByType(
        s4tkPackage: Package,
        resourceTypes: number[],
        options: ResourceExtractionOptions = {}
    ): ResourceEntry[] {
        const opts = {
            includeEmpty: false,
            maxResources: Infinity,
            ...options
        };

        // Use Set for O(1) lookup performance
        const typeSet = new Set(resourceTypes);
        const resources: ResourceEntry[] = [];

        for (const entry of s4tkPackage.entries.values()) {
            if (resources.length >= opts.maxResources) break;

            if (typeSet.has(entry.key.type)) {
                // Skip empty resources if not included
                if (!opts.includeEmpty && (!entry.value || (entry.value as Buffer).length === 0)) {
                    continue;
                }
                resources.push(entry);
            }
        }

        return resources;
    }

    /**
     * Gets all resources as an array (optimized)
     */
    public static getAllResources(s4tkPackage: Package): ResourceEntry[] {
        return Array.from(s4tkPackage.entries.values());
    }

    /**
     * Validates package structure without full parsing
     */
    public static validatePackageStructure(buffer: Buffer): {
        isValid: boolean;
        hasValidHeader: boolean;
        resourceCount: number;
        errors: string[];
    } {
        const result = {
            isValid: true,
            hasValidHeader: false,
            resourceCount: 0,
            errors: [] as string[]
        };

        try {
            // Check minimum file size
            if (buffer.length < 96) { // DBPF header minimum size
                result.errors.push('File too small to contain valid DBPF header');
                result.isValid = false;
                return result;
            }

            // Check DBPF signature
            const signature = buffer.toString('ascii', 0, 4);
            if (signature !== 'DBPF') {
                result.errors.push('Invalid DBPF signature');
                result.isValid = false;
                return result;
            }

            result.hasValidHeader = true;

            // Try to parse for resource count
            const s4tkPackage = Package.from(buffer, { 
                decompressBuffers: false, 
                loadRaw: true 
            });
            
            result.resourceCount = s4tkPackage.size;

            if (result.resourceCount === 0) {
                result.errors.push('Package contains no resources');
                result.isValid = false;
            }

        } catch (error) {
            result.errors.push(`Package parsing failed: ${error.message}`);
            result.isValid = false;
        }

        return result;
    }

    /**
     * Checks if a resource type exists in the package
     */
    public static hasResourceType(s4tkPackage: Package, resourceType: number): boolean {
        for (const entry of s4tkPackage.entries.values()) {
            if (entry.key.type === resourceType) {
                return true;
            }
        }
        return false;
    }

    /**
     * Gets resource count by type
     */
    public static getResourceCountByType(s4tkPackage: Package): Map<number, number> {
        const counts = new Map<number, number>();
        
        for (const entry of s4tkPackage.entries.values()) {
            const type = entry.key.type;
            counts.set(type, (counts.get(type) || 0) + 1);
        }
        
        return counts;
    }

    /**
     * Generates a cache key for package caching
     */
    private static generateCacheKey(
        buffer: Buffer, 
        fileName: string, 
        options: PackageParseOptions
    ): string {
        // Use file size and name for cache key (faster than full hash)
        const optionsKey = JSON.stringify({
            decompressBuffers: options.decompressBuffers,
            loadRaw: options.loadRaw
        });
        return `${fileName}-${buffer.length}-${optionsKey}`;
    }

    /**
     * Caches a parsed package
     */
    private static cachePackage(
        buffer: Buffer,
        fileName: string,
        options: PackageParseOptions,
        s4tkPackage: Package
    ): void {
        // Clean old cache entries if at limit
        if (this.packageCache.size >= this.MAX_CACHE_SIZE) {
            this.cleanCache();
        }

        const cacheKey = this.generateCacheKey(buffer, fileName, options);
        this.packageCache.set(cacheKey, {
            package: s4tkPackage,
            timestamp: Date.now()
        });
    }

    /**
     * Cleans expired cache entries
     */
    private static cleanCache(): void {
        const now = Date.now();
        const expiredKeys: string[] = [];

        for (const [key, value] of this.packageCache.entries()) {
            if (now - value.timestamp > this.CACHE_TTL) {
                expiredKeys.push(key);
            }
        }

        // Remove expired entries
        for (const key of expiredKeys) {
            this.packageCache.delete(key);
        }

        // If still at limit, remove oldest entries
        if (this.packageCache.size >= this.MAX_CACHE_SIZE) {
            const entries = Array.from(this.packageCache.entries())
                .sort((a, b) => a[1].timestamp - b[1].timestamp);
            
            const toRemove = entries.slice(0, Math.floor(this.MAX_CACHE_SIZE / 2));
            for (const [key] of toRemove) {
                this.packageCache.delete(key);
            }
        }
    }

    /**
     * Clears the package cache
     */
    public static clearCache(): void {
        this.packageCache.clear();
    }

    /**
     * Gets cache statistics
     */
    public static getCacheStats(): {
        size: number;
        maxSize: number;
        hitRate: number;
    } {
        // This would need hit/miss tracking for accurate hit rate
        return {
            size: this.packageCache.size,
            maxSize: this.MAX_CACHE_SIZE,
            hitRate: 0 // Placeholder - would need tracking
        };
    }
}
