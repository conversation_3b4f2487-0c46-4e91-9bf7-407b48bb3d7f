# 🚨 **SIMONITOR CRITICAL CONSOLIDATION - NEXT SESSION PROMPT**

## **CONTEXT**
You are continuing work on Simon<PERSON>, a Sims 4 mod management tool. The previous agent completed comprehensive redundancy cleanup across three rounds and discovered **critical architectural issues** that pose system stability risks. You must complete the consolidation work to prevent data inconsistencies.

## **CRITICAL SITUATION**
Simon<PERSON> currently has **TWO COMPLETE RESOURCE TYPE SYSTEMS** running in parallel, which could cause different services to categorize the same mod differently, leading to conflicts and inconsistent analysis results. This is a **SYSTEM-BREAKING** issue that requires immediate resolution.

## **YOUR MISSION**
Complete the critical consolidation phase to eliminate architectural redundancies and ensure system stability. Focus on the most critical issues first: dual resource type systems, incomplete error handler migration, and package management overlap.

## **REQUIRED READING (ESSENTIAL)**
**Read these documents in order before starting:**
1. `SIMONITOR_CURRENT_STATUS.md` - Current state and priorities
2. `ROUND_3_CRITICAL_CLEANUP.md` - Critical architectural issues
3. `NEXT_AGENT_INSTRUCTIONS.md` - Detailed step-by-step instructions
4. `REDUNDANCY_CLEANUP_REPORT.md` - Complete cleanup overview

## **✅ COMPLETED WORK (Phase 2)**

### **Error Handler Migration - 100% COMPLETE**
All error handling has been successfully unified:
- ✅ All 9 services migrated to `UnifiedErrorHandler`
- ✅ Legacy `ErrorHandler.ts` removed
- ✅ Consistent error categorization implemented
- ✅ Backward compatibility maintained with mapping functions

**Architecture Established:**
- Single source of truth: `src/services/shared/UnifiedErrorHandler.ts`
- Unified error categories and severity levels
- All services now use `UnifiedErrorHandler.createError()`

## **IMMEDIATE CRITICAL TASKS (PRIORITY ORDER)**

### **1. DUAL RESOURCE TYPE SYSTEMS (CRITICAL - DO FIRST)**
**Problem:** Two resource type systems coexist:
- `src/constants/unifiedResourceTypes.ts` (LEGACY)
- `src/constants/ResourceTypeRegistry.ts` (NEW)

**Actions:**
1. Search codebase for ALL imports from `unifiedResourceTypes.ts`
2. Replace with `ResourceTypeRegistry.ts` imports
3. Update all `URT.` references to use `RESOURCE_GROUPS.`
4. Delete `unifiedResourceTypes.ts` completely
5. Test thoroughly - this is the most critical change

### **2. PACKAGE MANAGEMENT CONSOLIDATION (NOW TOP PRIORITY)**
**Problem:** Duplicate functionality between `PackageManager.ts` and `PackageParserService.ts`

**Actions:**
1. Merge overlapping validation logic
2. Consolidate async/sync operations
3. Remove duplicate methods

## **SHARED SERVICES AVAILABLE**
The previous agent created these shared services for you to use:
- `UnifiedErrorHandler.ts` - Single error handling system
- `PackageParserService.ts` - Unified package parsing with caching
- `ResourceTypeRegistry.ts` - New unified resource type system
- `ScriptAnalysisUtils.ts` - Unified script analysis (10+ frameworks)
- `FileValidationService.ts` - Standardized file validation

## **CRITICAL WARNINGS**
- **DO NOT** change public APIs without careful consideration
- **ALWAYS** test each change thoroughly before moving to next
- **VERIFY** performance targets maintained (45ms analysis time)
- **ENSURE** accuracy targets preserved (90%+ accuracy)
- The resource type consolidation is the most critical - take extra care

## **SUCCESS CRITERIA**
- [ ] Single resource type system (no `unifiedResourceTypes.ts` imports)
- [ ] Unified error handling across all services
- [ ] Consolidated package management
- [ ] All tests pass
- [ ] Performance maintained
- [ ] No data inconsistencies

## **TESTING REQUIREMENTS**
After each major change:
1. Run existing test suite
2. Test with real mod files
3. Verify resource categorization consistency
4. Check error handling consistency
5. Monitor performance metrics

## **EXPECTED OUTCOME**
Upon completion, Simonitor will have eliminated critical architectural redundancies, ensuring system stability and consistent behavior across all services.

---

**START HERE:** Begin by reading `SIMONITOR_CURRENT_STATUS.md` to understand the current state, then follow the detailed instructions in `NEXT_AGENT_INSTRUCTIONS.md`. Focus on the resource type system consolidation first as it poses the highest risk to system stability.
