import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService.js';
import path from 'path';
import fs from 'fs';

async function debugUIData() {
    console.log('🔍 Debugging UI Data Structure...\n');

    const modsPath = 'C:/Users/<USER>/Documents/Electronic Arts/The Sims 4/Mods';

    // Get actual files from the mods folder
    const allFiles = fs.readdirSync(modsPath).filter(f => f.endsWith('.package'));
    const testFiles = allFiles.slice(0, 2); // Take first 2 files

    console.log(`Found ${allFiles.length} package files, testing with first 2:`);
    testFiles.forEach(f => console.log(`  - ${f}`));
    console.log();

    const analysisService = new PackageAnalysisService();

    for (const fileName of testFiles) {
        const filePath = path.join(modsPath, fileName);

        try {
            console.log(`\n📦 Analyzing: ${fileName}`);
            console.log('='.repeat(80));

            const buffer = fs.readFileSync(filePath);
            const result = await analysisService.detailedAnalyzeAsync(buffer, filePath);
            
            // Show the key fields that the UI uses
            console.log('\n🎯 UI-Relevant Data:');
            console.log(`fileName: "${result.fileName}"`);
            console.log(`actualModName: "${result.actualModName}"`);
            console.log(`actualDescription: "${result.actualDescription}"`);
            console.log(`hasStringTable: ${result.hasStringTable}`);

            // Debug the full result structure
            console.log('\n🔍 Full Result Keys:');
            console.log(Object.keys(result).join(', '));

            // Check if there's metadata in other places
            if (result.metadata) {
                console.log('\n📋 Metadata Object:');
                console.log(JSON.stringify(result.metadata, null, 2));
            }

            // Simulate App.vue data mapping
            console.log('\n🔄 Simulating App.vue Data Mapping:');
            const mappedData = {
                fileName: result.filePath ? result.filePath.split(/[/\\]/).pop() : result.fileName || 'Unknown',
                actualModName: result.intelligence?.stringTableData?.modName || result.metadata?.modName || result.modName || null,
                actualDescription: result.intelligence?.stringTableData?.description || result.metadata?.description || null,
                author: result.metadata?.author || result.author || null,
                hasStringTable: !!(result.intelligence?.stringTableData?.customStringCount > 0)
            };

            console.log(`  fileName: "${mappedData.fileName}"`);
            console.log(`  actualModName: "${mappedData.actualModName}"`);
            console.log(`  actualDescription: "${mappedData.actualDescription}"`);
            console.log(`  author: "${mappedData.author}"`);
            console.log(`  hasStringTable: ${mappedData.hasStringTable}`);
            
            if (result.stringTableData) {
                console.log('\n📋 StringTable Data:');
                console.log(`  modName: "${result.stringTableData.modName}"`);
                console.log(`  description: "${result.stringTableData.description}"`);
                console.log(`  itemNames: [${result.stringTableData.itemNames.join(', ')}]`);
                console.log(`  confidence: ${result.stringTableData.confidence}%`);
            }
            
            if (result.objectClassification) {
                console.log('\n🏷️ Object Classification:');
                console.log(`  category: "${result.objectClassification.category}"`);
                console.log(`  specificType: "${result.objectClassification.specificType}"`);
                console.log(`  confidence: ${result.objectClassification.confidence}%`);
            }
            
            if (result.universalClassification) {
                console.log('\n🌐 Universal Classification:');
                console.log(`  category: "${result.universalClassification.category}"`);
                console.log(`  subcategory: "${result.universalClassification.subcategory}"`);
                console.log(`  confidence: ${result.universalClassification.confidence}%`);
            }
            
            console.log('\n' + '='.repeat(80));
            
        } catch (error) {
            console.error(`❌ Error analyzing ${fileName}:`, error);
        }
    }
}

debugUIData().catch(console.error);
