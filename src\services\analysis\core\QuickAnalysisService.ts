import { Package } from '@s4tk/models';
import type { PackageFileReadingOptions } from '@s4tk/models/types';
import { FileTypeDetector } from './FileTypeDetector';
import { ModCategory } from '../../../types/analysis';
import { RESOURCE_GROUPS } from '../../../constants/ResourceTypeRegistry';
import { packageManager } from '../../s4tk';
import { UnifiedErrorHandler, UnifiedErrorCategory } from '../../shared/UnifiedErrorHandler';
import type { QuickAnalysisResult, EnhancedQuickAnalysisResult, CancellationToken } from '../../../types/analysis-results';

/**
 * Service responsible for quick package analysis
 * Focuses on fast categorization and basic resource detection
 */
export class QuickAnalysisService {
    
    /**
     * Performs quick synchronous analysis for immediate feedback
     */
    public analyze(buffer: Buffer, filePath: string): QuickAnalysisResult {
        const fileMetadata = FileTypeDetector.analyzeFile(filePath, buffer.length);

        if (fileMetadata.type !== 'package') {
            return {
                filePath,
                fileType: fileMetadata.type as any,
                category: fileMetadata.category as ModCategory,
                subcategory: fileMetadata.subcategory,
                fileSize: buffer.length,
                resourceCount: 1,
                hasConflictPotential: false,
                needsDeepAnalysis: fileMetadata.type === 'script', // Scripts need deep analysis
                resourceTypes: []
            };
        }

        try {
            const quickOptions: PackageFileReadingOptions = {
                resourceFilter: (type: number) => RESOURCE_GROUPS.CRITICAL_RESOURCES.includes(type),
                decompressBuffers: false,
                loadRaw: true,
                limit: 50
            };

            const resourceEntries = Package.extractResources(buffer, quickOptions);
            const resourceTypes = resourceEntries.map(entry => entry.key.type);

            return {
                category: this.determineBasicCategory(resourceTypes, filePath),
                resourceCount: resourceEntries.length,
                hasConflictPotential: this.hasOverrideResources(resourceTypes, resourceEntries),
                needsDeepAnalysis: this.requiresDetailedAnalysis(resourceTypes),
                resourceTypes
            };

        } catch (error) {
            const errorInfo = UnifiedErrorHandler.createError(
                error,
                'QuickAnalysisService.analyze',
                filePath,
                UnifiedErrorCategory.PACKAGE_LOADING
            );
            console.warn('Quick analysis failed:', errorInfo.message);

            return {
                category: ModCategory.UNKNOWN,
                resourceCount: 0,
                hasConflictPotential: false,
                needsDeepAnalysis: false,
                resourceTypes: []
            };
        }
    }

    /**
     * Performs enhanced async quick analysis with additional S4TK data
     */
    public async analyzeAsync(buffer: Buffer, filePath: string, cancellationToken?: CancellationToken): Promise<EnhancedQuickAnalysisResult> {
        const startTime = Date.now();
        const fileMetadata = FileTypeDetector.analyzeFile(filePath, buffer.length);

        if (fileMetadata.type !== 'package') {
            return {
                category: fileMetadata.category as ModCategory,
                resourceCount: 1,
                hasConflictPotential: false,
                needsDeepAnalysis: false,
                resourceTypes: [],
                compressionStats: {},
                validationIssues: [],
                s4tkVersion: '0.6.14',
                processingTime: Date.now() - startTime
            };
        }

        try {
            if (cancellationToken?.isCancelled) {
                throw new Error('Operation cancelled');
            }

            const quickOptions: PackageFileReadingOptions = {
                resourceFilter: (type: number) => RESOURCE_GROUPS.CRITICAL_RESOURCES.includes(type),
                decompressBuffers: false,
                loadRaw: true,
                limit: 50
            };

            const resourceEntries = await packageManager.extractResourcesAsync(buffer, quickOptions);
            
            if (cancellationToken?.isCancelled) {
                throw new Error('Operation cancelled');
            }

            const resourceTypes = resourceEntries.map(entry => entry.key.type);
            const compressionStats = this.analyzeCompressionTypes(resourceEntries);
            const validationIssues = await this.quickValidateResources(resourceEntries);

            return {
                category: this.determineBasicCategory(resourceTypes, filePath),
                resourceCount: resourceEntries.length,
                hasConflictPotential: this.hasOverrideResources(resourceTypes, resourceEntries),
                needsDeepAnalysis: this.requiresDetailedAnalysis(resourceTypes),
                resourceTypes,
                compressionStats,
                validationIssues,
                s4tkVersion: '0.6.14',
                processingTime: Date.now() - startTime
            };

        } catch (error) {
            const errorInfo = UnifiedErrorHandler.createError(
                error,
                'QuickAnalysisService.analyzeAsync',
                filePath,
                UnifiedErrorCategory.PACKAGE_LOADING
            );

            return {
                category: ModCategory.UNKNOWN,
                resourceCount: 0,
                hasConflictPotential: false,
                needsDeepAnalysis: false,
                resourceTypes: [],
                compressionStats: {},
                validationIssues: [errorInfo.message],
                s4tkVersion: '0.6.14',
                processingTime: Date.now() - startTime
            };
        }
    }

    // Private helper methods
    private determineBasicCategory(resourceTypes: number[], filePath: string): ModCategory {
        const fileName = filePath.toLowerCase();
        
        if (resourceTypes.some(type => RESOURCE_GROUPS.CAS_RESOURCES.includes(type))) {
            return ModCategory.CAS_CC;
        }

        if (resourceTypes.some(type => RESOURCE_GROUPS.OBJECT_RESOURCES.includes(type))) {
            return ModCategory.BUILD_BUY_CC;
        }

        if (resourceTypes.some(type => RESOURCE_GROUPS.CRITICAL_RESOURCES.includes(type))) {
            if (fileName.includes('override') || fileName.includes('basegame') || fileName.includes('fix')) {
                return ModCategory.OVERRIDE;
            }
            return ModCategory.TUNING_MOD;
        }
        
        if (fileName.endsWith('.ts4script')) {
            return ModCategory.SCRIPT_MOD;
        }
        
        return ModCategory.UNKNOWN;
    }

    private hasOverrideResources(resourceTypes: number[], resourceEntries?: any[]): boolean {
        const hasOverrideProneTypes = resourceTypes.some(type =>
            RESOURCE_GROUPS.CRITICAL_RESOURCES.includes(type)
        );
        
        if (!hasOverrideProneTypes) {
            return false;
        }
        
        if (resourceEntries) {
            return resourceEntries.some(entry => 
                entry.key.group === 0x80000000 ||
                entry.key.group === 0x00000000
            );
        }
        
        return hasOverrideProneTypes;
    }

    private requiresDetailedAnalysis(resourceTypes: number[]): boolean {
        if (resourceTypes.some(type =>
            RESOURCE_GROUPS.CAS_RESOURCES.includes(type) ||
            RESOURCE_GROUPS.OBJECT_RESOURCES.includes(type)
        )) {
            return true;
        }
        
        return resourceTypes.length > 10;
    }

    private analyzeCompressionTypes(resourceEntries: any[]): Record<string, number> {
        const compressionStats: Record<string, number> = {};
        
        resourceEntries.forEach(entry => {
            if (entry.value?.compressionType !== undefined) {
                const compressionName = this.getCompressionTypeName(entry.value.compressionType);
                compressionStats[compressionName] = (compressionStats[compressionName] || 0) + 1;
            }
        });
        
        return compressionStats;
    }

    private async quickValidateResources(resourceEntries: any[]): Promise<string[]> {
        const issues: string[] = [];
        
        for (const entry of resourceEntries.slice(0, 10)) {
            try {
                if (!entry.key || typeof entry.key.type !== 'number') {
                    issues.push(`Invalid resource key: ${entry.id}`);
                }
                
                if (!entry.value?.buffer || entry.value.buffer.length === 0) {
                    issues.push(`Empty resource buffer: ${entry.id}`);
                }
            } catch (error) {
                issues.push(`Validation error: ${error.message}`);
            }
        }
        
        return issues;
    }

    private getCompressionTypeName(compressionType: number): string {
        switch (compressionType) {
            case 0x0000: return 'Uncompressed';
            case 0xFFFE: return 'Streamable';
            case 0xFFFF: return 'Internal';
            case 0xFFE0: return 'Deleted';
            case 0x5A42: return 'ZLIB';
            default: return `Unknown (0x${compressionType.toString(16)})`;
        }
    }
}

// Export singleton instance
export const quickAnalysisService = new QuickAnalysisService();