/**
 * Visual Browser Integration Service
 * 
 * Integrates the visual category browser with the existing mod analysis system.
 * Handles thumbnail generation, caching, and data synchronization.
 * 
 * Addresses Reddit request: "visual categories with thumbnails and sidebar navigation"
 */

import type { ModData } from '../../types/ModData';
import type { DetailedAnalysisResult } from '../analysis/core/DetailedAnalysisService';
import { ThumbnailExtractionService, type ThumbnailData, type ThumbnailExtractionResult } from './ThumbnailExtractionService';
import { ContentAnalysisService } from '../analysis/content/ContentAnalysisService';

export interface VisualModData extends ModData {
    thumbnails: ThumbnailData[];
    primaryThumbnail?: ThumbnailData;
    visualTags: string[];
    colorPalette?: string[];
    visualCategory: string;
    visualSubcategory?: string;
    thumbnailGenerationStatus: 'pending' | 'processing' | 'completed' | 'failed';
    thumbnailGenerationError?: string;
}

export interface VisualBrowserState {
    mods: VisualModData[];
    thumbnailCache: Map<string, ThumbnailData[]>;
    processingQueue: string[];
    totalMods: number;
    processedMods: number;
    failedMods: number;
    isProcessing: boolean;
}

export interface ThumbnailGenerationOptions {
    batchSize?: number;
    maxConcurrent?: number;
    prioritizeVisible?: boolean;
    cacheEnabled?: boolean;
    generateFallbacks?: boolean;
    extractColorPalette?: boolean;
}

/**
 * Service for integrating visual browsing capabilities with mod analysis
 */
export class VisualBrowserIntegrationService {
    private static instance: VisualBrowserIntegrationService;
    private state: VisualBrowserState;
    private processingPromises: Map<string, Promise<void>>;
    private abortController: AbortController;

    private constructor() {
        this.state = {
            mods: [],
            thumbnailCache: new Map(),
            processingQueue: [],
            totalMods: 0,
            processedMods: 0,
            failedMods: 0,
            isProcessing: false
        };
        this.processingPromises = new Map();
        this.abortController = new AbortController();
    }

    public static getInstance(): VisualBrowserIntegrationService {
        if (!this.instance) {
            this.instance = new VisualBrowserIntegrationService();
        }
        return this.instance;
    }

    /**
     * Initializes the visual browser with mod data
     */
    public async initializeWithMods(
        mods: ModData[], 
        options: ThumbnailGenerationOptions = {}
    ): Promise<void> {
        const opts = {
            batchSize: 10,
            maxConcurrent: 3,
            prioritizeVisible: true,
            cacheEnabled: true,
            generateFallbacks: true,
            extractColorPalette: false,
            ...options
        };

        // Convert ModData to VisualModData
        this.state.mods = mods.map(mod => this.convertToVisualModData(mod));
        this.state.totalMods = mods.length;
        this.state.processedMods = 0;
        this.state.failedMods = 0;

        // Start thumbnail generation process
        if (opts.cacheEnabled) {
            await this.loadThumbnailCache();
        }

        await this.generateThumbnailsForMods(this.state.mods, opts);
    }

    /**
     * Converts ModData to VisualModData
     */
    private convertToVisualModData(mod: ModData): VisualModData {
        return {
            ...mod,
            thumbnails: [],
            visualTags: this.generateVisualTags(mod),
            visualCategory: this.determineVisualCategory(mod),
            visualSubcategory: this.determineVisualSubcategory(mod),
            thumbnailGenerationStatus: 'pending'
        };
    }

    /**
     * Generates visual tags based on mod analysis
     */
    private generateVisualTags(mod: ModData): string[] {
        const tags: string[] = [];

        // Add category-based tags
        if (mod.category) {
            tags.push(mod.category.toLowerCase());
        }

        // Add enhanced classification tags
        if (mod.objectClassification) {
            if (mod.objectClassification.category) {
                tags.push(mod.objectClassification.category.toLowerCase());
            }
            if (mod.objectClassification.roomAssignment) {
                tags.push(mod.objectClassification.roomAssignment.toLowerCase());
            }
            if (mod.objectClassification.functionality) {
                tags.push(...mod.objectClassification.functionality.map(f => f.toLowerCase()));
            }
        }

        if (mod.universalClassification) {
            if (mod.universalClassification.subcategory) {
                tags.push(mod.universalClassification.subcategory.toLowerCase());
            }
            if (mod.universalClassification.ageGroups) {
                tags.push(...mod.universalClassification.ageGroups.map(a => a.toLowerCase()));
            }
        }

        // Add quality tags
        if (mod.resourceCount && mod.resourceCount > 50) {
            tags.push('high-resource-count');
        }
        if (mod.fileSize && mod.fileSize > 1024 * 1024) {
            tags.push('large-file');
        }

        return [...new Set(tags)]; // Remove duplicates
    }

    /**
     * Determines visual category for browsing
     */
    private determineVisualCategory(mod: ModData): string {
        const category = mod.category?.toLowerCase() || '';
        
        if (category.includes('cas') || category.includes('create-a-sim')) {
            return 'Create-a-Sim';
        }
        if (category.includes('build') || category.includes('buy') || category.includes('object')) {
            return 'Build/Buy';
        }
        if (category.includes('script') || category.includes('python')) {
            return 'Script Mods';
        }
        if (category.includes('gameplay') || category.includes('trait') || category.includes('career')) {
            return 'Gameplay';
        }
        
        return 'Other';
    }

    /**
     * Determines visual subcategory for detailed browsing
     */
    private determineVisualSubcategory(mod: ModData): string | undefined {
        if (mod.objectClassification?.specificType) {
            return mod.objectClassification.specificType;
        }
        if (mod.universalClassification?.subcategory) {
            return mod.universalClassification.subcategory;
        }
        return undefined;
    }

    /**
     * Generates thumbnails for a batch of mods
     */
    public async generateThumbnailsForMods(
        mods: VisualModData[], 
        options: ThumbnailGenerationOptions = {}
    ): Promise<void> {
        const opts = {
            batchSize: 10,
            maxConcurrent: 3,
            ...options
        };

        this.state.isProcessing = true;

        try {
            // Process mods in batches
            for (let i = 0; i < mods.length; i += opts.batchSize) {
                const batch = mods.slice(i, i + opts.batchSize);
                
                // Process batch with concurrency limit
                const promises = batch.map(mod => this.generateThumbnailForMod(mod, options));
                await this.processConcurrently(promises, opts.maxConcurrent);
                
                // Check if processing was aborted
                if (this.abortController.signal.aborted) {
                    break;
                }
            }
        } finally {
            this.state.isProcessing = false;
        }
    }

    /**
     * Generates thumbnail for a single mod
     */
    private async generateThumbnailForMod(
        mod: VisualModData, 
        options: ThumbnailGenerationOptions = {}
    ): Promise<void> {
        if (this.processingPromises.has(mod.id)) {
            return this.processingPromises.get(mod.id);
        }

        const promise = this.doGenerateThumbnailForMod(mod, options);
        this.processingPromises.set(mod.id, promise);

        try {
            await promise;
        } finally {
            this.processingPromises.delete(mod.id);
        }
    }

    /**
     * Internal thumbnail generation logic
     */
    private async doGenerateThumbnailForMod(
        mod: VisualModData, 
        options: ThumbnailGenerationOptions = {}
    ): Promise<void> {
        mod.thumbnailGenerationStatus = 'processing';

        try {
            // Check cache first
            if (options.cacheEnabled && this.state.thumbnailCache.has(mod.id)) {
                const cachedThumbnails = this.state.thumbnailCache.get(mod.id)!;
                mod.thumbnails = cachedThumbnails;
                mod.primaryThumbnail = cachedThumbnails[0];
                mod.thumbnailGenerationStatus = 'completed';
                this.state.processedMods++;
                return;
            }

            // Load mod file
            const fs = await import('fs');
            if (!fs.existsSync(mod.filePath)) {
                throw new Error('Mod file not found');
            }

            const buffer = fs.readFileSync(mod.filePath);

            // Extract thumbnails
            const result = await ThumbnailExtractionService.extractThumbnails(
                buffer,
                mod.fileName,
                {
                    maxThumbnails: 5,
                    preferredFormat: 'webp',
                    maxWidth: 256,
                    maxHeight: 256,
                    prioritizeCasThumbnails: true,
                    includeTextures: true,
                    generateFallbacks: options.generateFallbacks
                }
            );

            if (result.success && result.thumbnails.length > 0) {
                mod.thumbnails = result.thumbnails;
                mod.primaryThumbnail = result.thumbnails[0];
                
                // Extract color palette if requested
                if (options.extractColorPalette) {
                    mod.colorPalette = await this.extractColorPalette(result.thumbnails[0]);
                }

                // Cache thumbnails
                if (options.cacheEnabled) {
                    this.state.thumbnailCache.set(mod.id, result.thumbnails);
                }

                mod.thumbnailGenerationStatus = 'completed';
                this.state.processedMods++;
            } else {
                throw new Error(result.errors.join(', ') || 'No thumbnails extracted');
            }

        } catch (error) {
            mod.thumbnailGenerationStatus = 'failed';
            mod.thumbnailGenerationError = error.message;
            this.state.failedMods++;
            console.warn(`Failed to generate thumbnail for ${mod.fileName}:`, error);
        }
    }

    /**
     * Processes promises with concurrency limit
     */
    private async processConcurrently<T>(promises: Promise<T>[], maxConcurrent: number): Promise<T[]> {
        const results: T[] = [];
        const executing: Promise<void>[] = [];

        for (const promise of promises) {
            const wrappedPromise = promise.then(result => {
                results.push(result);
            });

            executing.push(wrappedPromise);

            if (executing.length >= maxConcurrent) {
                await Promise.race(executing);
                executing.splice(executing.findIndex(p => p === wrappedPromise), 1);
            }
        }

        await Promise.all(executing);
        return results;
    }

    /**
     * Extracts color palette from thumbnail
     */
    private async extractColorPalette(thumbnail: ThumbnailData): Promise<string[]> {
        // This would require additional image processing libraries
        // For now, return a placeholder
        return ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0'];
    }

    /**
     * Loads thumbnail cache from storage
     */
    private async loadThumbnailCache(): Promise<void> {
        try {
            // Implementation would load from localStorage or file system
            // For now, start with empty cache
            this.state.thumbnailCache.clear();
        } catch (error) {
            console.warn('Failed to load thumbnail cache:', error);
        }
    }

    /**
     * Saves thumbnail cache to storage
     */
    public async saveThumbnailCache(): Promise<void> {
        try {
            // Implementation would save to localStorage or file system
            console.log('Saving thumbnail cache...');
        } catch (error) {
            console.warn('Failed to save thumbnail cache:', error);
        }
    }

    /**
     * Gets the current visual browser state
     */
    public getState(): VisualBrowserState {
        return { ...this.state };
    }

    /**
     * Gets mods filtered by visual category
     */
    public getModsByVisualCategory(category: string): VisualModData[] {
        return this.state.mods.filter(mod => mod.visualCategory === category);
    }

    /**
     * Searches mods by visual tags
     */
    public searchModsByVisualTags(tags: string[]): VisualModData[] {
        return this.state.mods.filter(mod => 
            tags.some(tag => mod.visualTags.includes(tag.toLowerCase()))
        );
    }

    /**
     * Gets processing progress
     */
    public getProgress(): { processed: number; total: number; failed: number; percentage: number } {
        const { processedMods, totalMods, failedMods } = this.state;
        return {
            processed: processedMods,
            total: totalMods,
            failed: failedMods,
            percentage: totalMods > 0 ? Math.round((processedMods / totalMods) * 100) : 0
        };
    }

    /**
     * Aborts thumbnail generation
     */
    public abortProcessing(): void {
        this.abortController.abort();
        this.state.isProcessing = false;
    }

    /**
     * Regenerates thumbnails for specific mods
     */
    public async regenerateThumbnails(modIds: string[]): Promise<void> {
        const modsToRegenerate = this.state.mods.filter(mod => modIds.includes(mod.id));
        
        // Clear cache for these mods
        modIds.forEach(id => this.state.thumbnailCache.delete(id));
        
        // Reset status
        modsToRegenerate.forEach(mod => {
            mod.thumbnailGenerationStatus = 'pending';
            mod.thumbnails = [];
            mod.primaryThumbnail = undefined;
            mod.thumbnailGenerationError = undefined;
        });

        // Regenerate
        await this.generateThumbnailsForMods(modsToRegenerate);
    }

    /**
     * Clears all cached data
     */
    public clearCache(): void {
        this.state.thumbnailCache.clear();
        this.state.mods.forEach(mod => {
            mod.thumbnails = [];
            mod.primaryThumbnail = undefined;
            mod.thumbnailGenerationStatus = 'pending';
            mod.thumbnailGenerationError = undefined;
        });
    }
}
